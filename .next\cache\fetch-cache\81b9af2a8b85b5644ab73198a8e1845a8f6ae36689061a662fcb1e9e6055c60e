{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef5ccc503713-MIA", "connection": "close", "content-encoding": "br", "content-location": "/opas?dependency_id=eq.9dbf151a-0111-4880-beb3-0c4b0723ffc7&select=%2A", "content-profile": "public", "content-range": "*/0", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:48 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=1oJPqjitfOhD5QNqr.aJqEdh_RfRct7VNOiRZakcPl4-1751425668-1.0.1.1-OsgCjcM4Crl.fxPTEQ2efx79MPczhIhIrxxlszovNRQEq3OQ13CpzECia_feV5YfqbadxeufzRTHV.c5PDBf9YKH8nA_iUPQSiWrZvRfFfc; path=/; expires=Wed, 02-Jul-25 03:37:48 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "1"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/opas?select=*&dependency_id=eq.9dbf151a-0111-4880-beb3-0c4b0723ffc7"}, "revalidate": 31536000, "tags": []}