{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef752d3cfa30-BOG", "connection": "close", "content-encoding": "br", "content-location": "/opas?dependency_id=eq.6c51c8c2-5543-4200-a0f7-be231dee9553&select=%2A", "content-profile": "public", "content-range": "0-108/109", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:52 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=U03ncn0HNjsYMuBDISeKsRlk00bc_yyOpJoIDi1iGrI-1751425672-1.0.1.1-8vZAinzSCuGR7tRXNBzRA06HFk4umYq3gExxEFKICgq2unIG04VrN6kx6LNsyCoT7MRVwHfxBDBUmU_ZyQQSGZU7.sU8_oyq7_h3ptRFyOE; path=/; expires=Wed, 02-Jul-25 03:37:52 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "6"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/opas?select=*&dependency_id=eq.6c51c8c2-5543-4200-a0f7-be231dee9553"}, "revalidate": 31536000, "tags": []}