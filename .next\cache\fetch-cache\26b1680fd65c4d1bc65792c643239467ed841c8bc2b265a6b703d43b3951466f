{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef77cc5a4c18-MIA", "connection": "close", "content-encoding": "br", "content-location": "/procedures?dependency_id=eq.7d2dc1c1-66c2-4da9-a0f9-5ffc34550bbf&select=%2A", "content-profile": "public", "content-range": "0-1/2", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:52 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=RmobzxbM5zD8U15KCrY2UVnF013zGeZYo667V9_ndHs-1751425672-1.0.1.1-1LQ_OwJUKd1gMdRY0vtZdh5riH53yrxsSXkOJpYr_RAKMvO.isvVRWEsXoBX03KN7U2d.A0Ytbo2Pc6tJlmIxDUzFGsLbJEMp71bq1MW1Pw; path=/; expires=Wed, 02-Jul-25 03:37:52 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/procedures?select=*&dependency_id=eq.7d2dc1c1-66c2-4da9-a0f9-5ffc34550bbf"}, "revalidate": 31536000, "tags": []}