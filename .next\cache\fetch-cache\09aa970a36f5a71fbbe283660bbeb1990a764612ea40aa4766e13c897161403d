{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958b10e8ea198752-MIA", "connection": "close", "content-encoding": "br", "content-location": "/subdependencies?dependency_id=eq.8525824e-448e-470d-9911-69c19ad65bc8&is_active=eq.true&select=%2A", "content-profile": "public", "content-range": "0-2/3", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:30:42 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=zgRZ0kNui.ezqLbx1VEv4IEjaH3kInbN8Rfu.T3l7Dc-1751427042-1.0.1.1-TCOirwadmTQGJMbe1MIal_XiAONxVpeabZJgZNIbvIkRNLyLp.e2usiajz.i3s3k.wEoP17acY62GoT3eRB3n56bFVOOE.BmudK5t.ps2Qw; path=/; expires=Wed, 02-Jul-25 04:00:42 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/subdependencies?select=*&dependency_id=eq.8525824e-448e-470d-9911-69c19ad65bc8&is_active=eq.true"}, "revalidate": 31536000, "tags": []}