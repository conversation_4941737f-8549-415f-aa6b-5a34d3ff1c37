{"name": "@axe-core/cli", "version": "4.10.2", "description": "A CLI for accessibility testing using axe-core", "author": {"name": "<PERSON><PERSON><PERSON>", "organization": "Deque Systems, Inc.", "url": "http://github.com/wilcofiers/"}, "contributors": [{"name": "<PERSON> (<EMAIL>)"}], "repository": {"type": "git", "url": "https://github.com/dequelabs/axe-core-npm.git"}, "license": "MPL-2.0", "files": ["dist", "postinstall.js"], "main": "dist/src/lib/index.js", "typings": "dist/src/types.d.ts", "bin": {"axe": "./dist/src/bin/cli.js"}, "publishConfig": {"access": "public"}, "engines": {"node": ">=8"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "test": "mocha --timeout 60000 -r ts-node/register 'src/**/**.test.ts'", "coverage": "nyc npm run test"}, "keywords": ["axe-core", "accessibility", "a11y", "wcag", "cli", "testing"], "dependencies": {"@axe-core/webdriverjs": "^4.10.2", "axe-core": "~4.10.3", "chromedriver": "latest", "colors": "^1.4.0", "commander": "^9.4.1", "dotenv": "^16.4.5", "selenium-webdriver": "~4.22.0"}, "devDependencies": {"@types/chai": "^4.3.3", "@types/chromedriver": "^81.0.1", "@types/mocha": "^10.0.0", "@types/selenium-webdriver": "^4.1.5", "@types/sinon": "^17.0.3", "chai": "^4.3.6", "execa": "5.1.1", "mocha": "^10.0.0", "nyc": "^17.1.0", "rimraf": "^6.0.1", "sinon": "^18.0.0", "tempy": "^1.0.0", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "nyc": {"checkCoverage": true, "extension": [".ts"], "reporter": ["text-summary", "html"], "statements": 95, "branches": 91, "functions": 94, "lines": 95, "exclude": ["dist", "coverage", "**/*.test.ts", "src/testutils"]}, "gitHead": "1055c5d005e72ad190987c358ebb361631909a85"}