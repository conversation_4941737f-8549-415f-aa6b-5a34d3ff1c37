{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef6629681277-MIA", "connection": "close", "content-encoding": "br", "content-location": "/procedures?dependency_id=eq.dd6ed28b-c972-4d6e-a994-1c9e4c937018&select=%2A", "content-profile": "public", "content-range": "*/0", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:50 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=JkiMHnnoSPcAMeWTKV0a4TH.GMPJLmxMP89.FqZs.vs-1751425670-1.0.1.1-SmxxWVBBZMVODgqMrOz1AjWrZqj77E9bzkMvdrjRnn06lalHsVh1lvhkWCmIH9.4hlKzwawN9xeL80qA7x6.gf0bK2dkToxZCXNX6U3veok; path=/; expires=Wed, 02-Jul-25 03:37:50 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "1"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/procedures?select=*&dependency_id=eq.dd6ed28b-c972-4d6e-a994-1c9e4c937018"}, "revalidate": 31536000, "tags": []}