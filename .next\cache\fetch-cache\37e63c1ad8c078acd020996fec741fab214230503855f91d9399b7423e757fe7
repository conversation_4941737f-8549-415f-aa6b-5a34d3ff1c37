{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef9c6812498a-MIA", "connection": "close", "content-encoding": "br", "content-location": "/opas?dependency_id=eq.2a8052a4-2d59-4b68-a7fc-df811e381801&select=%2A", "content-profile": "public", "content-range": "0-48/49", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:58 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=moOM8BkZawox4XUEqiZDBSnvF.g_AFF2gjJRZuMy.n0-1751425678-1.0.1.1-54xsmEI_NBGQ7u3iQ91GtMymiQYNwyS2Z.lGIhOBvgM9.3niqFJm8VCTK.cGWeYGbrkpb8MQpOyUIHDeB1Puc7pEpZtKWuNQCEzOj77SrOk; path=/; expires=Wed, 02-Jul-25 03:37:58 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/opas?select=*&dependency_id=eq.2a8052a4-2d59-4b68-a7fc-df811e381801"}, "revalidate": 31536000, "tags": []}