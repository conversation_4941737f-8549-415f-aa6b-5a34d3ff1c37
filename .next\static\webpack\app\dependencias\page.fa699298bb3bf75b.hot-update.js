"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dependencias/page",{

/***/ "(app-pages-browser)/./lib/services/dependencyService.ts":
/*!*******************************************!*\
  !*** ./lib/services/dependencyService.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./lib/supabase/client.ts\");\n\n/**\n * Servicio para gestionar datos de dependencias municipales\n * Procesa información de trámites y OPAs agrupados por dependencia\n */ class DependencyService {\n    static getInstance() {\n        if (!DependencyService.instance) {\n            DependencyService.instance = new DependencyService();\n        }\n        return DependencyService.instance;\n    }\n    /**\n   * Inicializa el servicio con las 14 dependencias oficiales del municipio\n   */ async initialize() {\n        if (this.initialized) return;\n        try {\n            // Usar las dependencias oficiales en lugar de procesar datos dinámicamente\n            await this.createOfficialDependencies();\n            this.initialized = true;\n        } catch (error) {\n            console.error(\"Error inicializando DependencyService:\", error);\n            // Fallback: usar el método anterior si falla\n            try {\n                await this.processDependencies();\n                this.initialized = true;\n            } catch (fallbackError) {\n                console.error(\"Error en fallback:\", fallbackError);\n                throw error;\n            }\n        }\n    }\n    /**\n   * Procesa los datos de trámites y OPAs para extraer información de dependencias\n   */ async processDependencies() {\n        const dependencyMap = new Map();\n        try {\n            // Obtener trámites desde Supabase\n            const { data: tramitesData, error: tramitesError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"procedures\").select(\"*\");\n            if (tramitesError) {\n                console.error(\"Error obteniendo tr\\xe1mites:\", tramitesError);\n                throw tramitesError;\n            }\n            // Procesar trámites\n            tramitesData === null || tramitesData === void 0 ? void 0 : tramitesData.forEach((tramite)=>{\n                const depCode = tramite.dependency_code || tramite.codigo_dependencia || \"unknown\";\n                const depName = tramite.dependency_name || tramite.dependencia || \"Dependencia Desconocida\";\n                if (!dependencyMap.has(depCode)) {\n                    dependencyMap.set(depCode, {\n                        name: depName,\n                        sigla: this.extractSigla(depName),\n                        tramites: [],\n                        opas: [],\n                        subdependenciasCount: 0 // Se calculará después\n                    });\n                }\n                dependencyMap.get(depCode).tramites.push(tramite);\n            });\n            // Obtener OPAs desde Supabase\n            const { data: opasData, error: opasError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"opas\").select(\"*\");\n            if (opasError) {\n                console.error(\"Error obteniendo OPAs:\", opasError);\n                throw opasError;\n            }\n            // Procesar OPAs\n            opasData === null || opasData === void 0 ? void 0 : opasData.forEach((opa)=>{\n                const depCode = opa.dependency_code || opa.codigo_dependencia || \"unknown\";\n                const depName = opa.dependency_name || opa.dependencia || \"Dependencia Desconocida\";\n                if (!dependencyMap.has(depCode)) {\n                    dependencyMap.set(depCode, {\n                        name: depName,\n                        sigla: this.extractSigla(depName),\n                        tramites: [],\n                        opas: [],\n                        subdependenciasCount: 0 // Se calculará después\n                    });\n                }\n                dependencyMap.get(depCode).opas.push(opa);\n            });\n        } catch (error) {\n            console.error(\"Error procesando dependencias:\", error);\n            throw error;\n        }\n        // Convertir a formato Dependency\n        dependencyMap.forEach((depData, depCode)=>{\n            const dependency = {\n                id: depCode,\n                name: depData.name,\n                sigla: depData.sigla,\n                tramitesCount: depData.tramites.length,\n                opasCount: depData.opas.length,\n                totalProcedures: depData.tramites.length + depData.opas.length,\n                subdependenciasCount: depData.subdependenciasCount || 0,\n                description: this.generateDescription(depData.name),\n                icon: this.assignIcon(depData.name),\n                color: this.assignColor(depCode)\n            };\n            this.dependencies.set(depCode, dependency);\n        });\n    }\n    /**\n   * Crear las 14 dependencias oficiales del municipio de Chía\n   */ async createOfficialDependencies() {\n        // Definir las 14 dependencias oficiales del municipio de Chía\n        const officialDependencies = [\n            {\n                id: \"despacho-alcalde\",\n                name: \"Despacho del Alcalde\",\n                sigla: \"DA\"\n            },\n            {\n                id: \"secretaria-general\",\n                name: \"Secretar\\xeda General\",\n                sigla: \"SG\"\n            },\n            {\n                id: \"secretaria-gobierno\",\n                name: \"Secretar\\xeda de Gobierno y Convivencia\",\n                sigla: \"SGC\"\n            },\n            {\n                id: \"secretaria-hacienda\",\n                name: \"Secretar\\xeda de Hacienda\",\n                sigla: \"SH\"\n            },\n            {\n                id: \"secretaria-planeacion\",\n                name: \"Secretar\\xeda de Planeaci\\xf3n\",\n                sigla: \"SP\"\n            },\n            {\n                id: \"secretaria-desarrollo-social\",\n                name: \"Secretar\\xeda de Desarrollo Social\",\n                sigla: \"SDS\"\n            },\n            {\n                id: \"secretaria-educacion\",\n                name: \"Secretar\\xeda de Educaci\\xf3n\",\n                sigla: \"SE\"\n            },\n            {\n                id: \"secretaria-salud\",\n                name: \"Secretar\\xeda de Salud\",\n                sigla: \"SS\"\n            },\n            {\n                id: \"secretaria-movilidad\",\n                name: \"Secretar\\xeda de Movilidad\",\n                sigla: \"SM\"\n            },\n            {\n                id: \"secretaria-desarrollo-economico\",\n                name: \"Secretar\\xeda de Desarrollo Econ\\xf3mico\",\n                sigla: \"SDE\"\n            },\n            {\n                id: \"secretaria-infraestructura\",\n                name: \"Secretar\\xeda de Infraestructura\",\n                sigla: \"SI\"\n            },\n            {\n                id: \"secretaria-ambiente\",\n                name: \"Secretar\\xeda de Ambiente\",\n                sigla: \"SA\"\n            },\n            {\n                id: \"secretaria-tecnologias\",\n                name: \"Secretar\\xeda de Tecnolog\\xedas de la Informaci\\xf3n\",\n                sigla: \"STI\"\n            },\n            {\n                id: \"entidades-descentralizadas\",\n                name: \"Entidades Descentralizadas\",\n                sigla: \"ED\"\n            }\n        ];\n        // Obtener contadores reales de trámites y OPAs\n        const counters = await this.getDependencyCounters();\n        // Crear cada dependencia oficial\n        officialDependencies.forEach((depInfo, index)=>{\n            const counter = counters.get(depInfo.name) || {\n                tramites: 0,\n                opas: 0,\n                subdependencies: 0\n            };\n            const dependency = {\n                id: depInfo.id,\n                name: depInfo.name,\n                sigla: depInfo.sigla,\n                tramitesCount: counter.tramites,\n                opasCount: counter.opas,\n                totalProcedures: counter.tramites + counter.opas,\n                subdependenciasCount: counter.subdependencies,\n                description: this.generateDescription(depInfo.name),\n                icon: this.assignIcon(depInfo.name),\n                color: this.assignColor(depInfo.id)\n            };\n            this.dependencies.set(depInfo.id, dependency);\n        });\n    }\n    /**\n   * Obtener contadores de trámites y OPAs por dependencia\n   */ async getDependencyCounters() {\n        const counters = new Map();\n        try {\n            var // Procesar trámites\n            _tramitesResult_data, // Procesar OPAs\n            _opasResult_data;\n            // Obtener datos de Supabase con JOIN para obtener nombres de dependencias\n            const [tramitesResult, opasResult] = await Promise.all([\n                _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"procedures\").select(\"\\n            id,\\n            dependencies!inner(name, code)\\n          \"),\n                _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"opas\").select(\"\\n            id,\\n            dependencies!inner(name, code)\\n          \")\n            ]);\n            (_tramitesResult_data = tramitesResult.data) === null || _tramitesResult_data === void 0 ? void 0 : _tramitesResult_data.forEach((item)=>{\n                var _item_dependencies;\n                const depName = this.mapToOfficialDependency((_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.name);\n                if (!counters.has(depName)) {\n                    counters.set(depName, {\n                        tramites: 0,\n                        opas: 0,\n                        subdependencies: 0\n                    });\n                }\n                counters.get(depName).tramites++;\n            });\n            (_opasResult_data = opasResult.data) === null || _opasResult_data === void 0 ? void 0 : _opasResult_data.forEach((item)=>{\n                var _item_dependencies;\n                const depName = this.mapToOfficialDependency((_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.name);\n                if (!counters.has(depName)) {\n                    counters.set(depName, {\n                        tramites: 0,\n                        opas: 0,\n                        subdependencies: 0\n                    });\n                }\n                counters.get(depName).opas++;\n            });\n        } catch (error) {\n            console.error(\"Error obteniendo contadores:\", error);\n        }\n        return counters;\n    }\n    /**\n   * Mapear nombres de dependencias de los datos a las dependencias oficiales\n   */ mapToOfficialDependency(originalName) {\n        if (!originalName) return \"Entidades Descentralizadas\";\n        const mappings = {\n            \"Despacho Alcalde\": \"Despacho del Alcalde\",\n            \"Secretaria General\": \"Secretar\\xeda General\",\n            \"Secretaria de Gobierno y Convivencia\": \"Secretar\\xeda de Gobierno y Convivencia\",\n            \"Secretaria de Hacienda\": \"Secretar\\xeda de Hacienda\",\n            \"Secretaria de Planeacion\": \"Secretar\\xeda de Planeaci\\xf3n\",\n            \"Secretaria de Desarrollo Social\": \"Secretar\\xeda de Desarrollo Social\",\n            \"Secretaria de Educacion\": \"Secretar\\xeda de Educaci\\xf3n\",\n            \"Secretaria de Salud\": \"Secretar\\xeda de Salud\",\n            \"Secretaria de Movilidad\": \"Secretar\\xeda de Movilidad\",\n            \"Secretaria para el Desarrollo Economico\": \"Secretar\\xeda de Desarrollo Econ\\xf3mico\",\n            \"Secretaria de Infraestructura\": \"Secretar\\xeda de Infraestructura\",\n            \"Secretaria de Medio Ambiente\": \"Secretar\\xeda de Ambiente\",\n            \"Secretaria de Participacion Ciudadana y Accion Comunitaria\": \"Secretar\\xeda de Gobierno y Convivencia\",\n            \"Descentralizados\": \"Entidades Descentralizadas\"\n        };\n        return mappings[originalName] || originalName;\n    }\n    /**\n   * Extrae sigla de un nombre de dependencia\n   */ extractSigla(name) {\n        if (name.includes(\"Secretar\\xeda\")) {\n            const words = name.replace(\"Secretar\\xeda de \", \"\").split(\" \");\n            return words.map((word)=>word.charAt(0).toUpperCase()).join(\"\");\n        }\n        const words = name.split(\" \");\n        if (words.length === 1) return words[0].substring(0, 3).toUpperCase();\n        return words.map((word)=>word.charAt(0).toUpperCase()).join(\"\").substring(0, 4);\n    }\n    /**\n   * Genera descripción para una dependencia\n   */ generateDescription(name) {\n        const descriptions = {\n            \"Despacho del Alcalde\": \"Oficina principal del Alcalde Municipal y coordinaci\\xf3n general\",\n            \"Secretar\\xeda General\": \"Gesti\\xf3n administrativa, jur\\xeddica y de archivo municipal\",\n            \"Secretar\\xeda de Gobierno y Convivencia\": \"Orden p\\xfablico, convivencia ciudadana y participaci\\xf3n comunitaria\",\n            \"Secretar\\xeda de Hacienda\": \"Administraci\\xf3n financiera, tributaria y presupuestal municipal\",\n            \"Secretar\\xeda de Planeaci\\xf3n\": \"Ordenamiento territorial, desarrollo urbano y gesti\\xf3n de proyectos\",\n            \"Secretar\\xeda de Desarrollo Social\": \"Programas sociales, atenci\\xf3n a poblaci\\xf3n vulnerable y desarrollo comunitario\",\n            \"Secretar\\xeda de Educaci\\xf3n\": \"Administraci\\xf3n del sistema educativo municipal y programas pedag\\xf3gicos\",\n            \"Secretar\\xeda de Salud\": \"Vigilancia epidemiol\\xf3gica, control sanitario y salud p\\xfablica\",\n            \"Secretar\\xeda de Movilidad\": \"Gesti\\xf3n del tr\\xe1nsito, transporte p\\xfablico y movilidad sostenible\",\n            \"Secretar\\xeda de Desarrollo Econ\\xf3mico\": \"Promoci\\xf3n empresarial, turismo y desarrollo econ\\xf3mico local\",\n            \"Secretar\\xeda de Infraestructura\": \"Obras p\\xfablicas, mantenimiento vial y proyectos de infraestructura\",\n            \"Secretar\\xeda de Ambiente\": \"Gesti\\xf3n ambiental, recursos naturales y sostenibilidad\",\n            \"Secretar\\xeda de Tecnolog\\xedas de la Informaci\\xf3n\": \"Sistemas de informaci\\xf3n, gobierno digital y tecnolog\\xeda\",\n            \"Entidades Descentralizadas\": \"Empresas de servicios p\\xfablicos y entidades adscritas al municipio\"\n        };\n        return descriptions[name] || \"Gesti\\xf3n de procedimientos administrativos de \".concat(name);\n    }\n    /**\n   * Asigna icono representativo para una dependencia\n   */ assignIcon(name) {\n        const icons = {\n            \"Despacho del Alcalde\": \"crown\",\n            \"Secretar\\xeda General\": \"folder\",\n            \"Secretar\\xeda de Gobierno y Convivencia\": \"shield\",\n            \"Secretar\\xeda de Hacienda\": \"banknote\",\n            \"Secretar\\xeda de Planeaci\\xf3n\": \"map\",\n            \"Secretar\\xeda de Desarrollo Social\": \"users\",\n            \"Secretar\\xeda de Educaci\\xf3n\": \"graduation-cap\",\n            \"Secretar\\xeda de Salud\": \"heart-pulse\",\n            \"Secretar\\xeda de Movilidad\": \"car\",\n            \"Secretar\\xeda de Desarrollo Econ\\xf3mico\": \"building\",\n            \"Secretar\\xeda de Infraestructura\": \"building\",\n            \"Secretar\\xeda de Ambiente\": \"folder\",\n            \"Secretar\\xeda de Tecnolog\\xedas de la Informaci\\xf3n\": \"folder\",\n            \"Entidades Descentralizadas\": \"building\"\n        };\n        return icons[name] || \"folder\";\n    }\n    /**\n   * Asigna color temático para una dependencia\n   */ assignColor(depCode) {\n        const colors = [\n            \"bg-chia-blue-100 border-chia-blue-300 hover:bg-chia-blue-200\",\n            \"bg-chia-green-100 border-chia-green-300 hover:bg-chia-green-200\",\n            \"bg-blue-100 border-blue-300 hover:bg-blue-200\",\n            \"bg-green-100 border-green-300 hover:bg-green-200\",\n            \"bg-purple-100 border-purple-300 hover:bg-purple-200\",\n            \"bg-orange-100 border-orange-300 hover:bg-orange-200\",\n            \"bg-teal-100 border-teal-300 hover:bg-teal-200\",\n            \"bg-indigo-100 border-indigo-300 hover:bg-indigo-200\",\n            \"bg-pink-100 border-pink-300 hover:bg-pink-200\"\n        ];\n        const index = parseInt(depCode) % colors.length;\n        return colors[index];\n    }\n    /**\n   * Obtiene todas las dependencias directamente desde la base de datos\n   */ async getAllDependencies() {\n        try {\n            // Obtener dependencias con contadores desde la base de datos\n            const { data: dependenciesData, error: dependenciesError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"dependencies\").select(\"id, name, code, acronym\").order(\"name\");\n            if (dependenciesError) {\n                console.error(\"Error obteniendo dependencias:\", dependenciesError);\n                return [];\n            }\n            // Obtener contadores de trámites y OPAs para cada dependencia\n            const dependencies = [];\n            for (const dep of dependenciesData || []){\n                // Contar trámites\n                const { count: tramitesCount } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"procedures\").select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"dependency_id\", dep.id);\n                // Contar OPAs\n                const { count: opasCount } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"opas\").select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"dependency_id\", dep.id);\n                // Contar subdependencias activas\n                const { count: subdependenciasCount } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"subdependencies\").select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"dependency_id\", dep.id).eq(\"is_active\", true);\n                const dependency = {\n                    id: dep.id,\n                    name: dep.name,\n                    sigla: dep.acronym || dep.code || \"\",\n                    tramitesCount: tramitesCount || 0,\n                    opasCount: opasCount || 0,\n                    totalProcedures: (tramitesCount || 0) + (opasCount || 0),\n                    subdependenciasCount: subdependenciasCount || 0,\n                    description: this.generateDescription(dep.name),\n                    icon: this.assignIcon(dep.name),\n                    color: this.assignColor(dep.id)\n                };\n                dependencies.push(dependency);\n            }\n            return dependencies.sort((a, b)=>b.totalProcedures - a.totalProcedures);\n        } catch (error) {\n            console.error(\"Error en getAllDependencies:\", error);\n            return [];\n        }\n    }\n    /**\n   * Obtiene una dependencia por ID\n   */ async getDependencyById(id) {\n        await this.initialize();\n        return this.dependencies.get(id) || null;\n    }\n    /**\n   * Obtiene estadísticas generales de dependencias directamente desde la base de datos\n   */ async getDependencyStats() {\n        try {\n            // Contar dependencias\n            const { count: totalDependencies } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"dependencies\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            });\n            // Contar trámites\n            const { count: totalTramites } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"procedures\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            });\n            // Contar OPAs\n            const { count: totalOPAs } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"opas\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            });\n            const totalProcedures = (totalTramites || 0) + (totalOPAs || 0);\n            const averageProceduresPerDependency = totalDependencies > 0 ? Math.round(totalProcedures / totalDependencies) : 0;\n            return {\n                totalDependencies: totalDependencies || 0,\n                totalTramites: totalTramites || 0,\n                totalOPAs: totalOPAs || 0,\n                totalProcedures,\n                averageProceduresPerDependency\n            };\n        } catch (error) {\n            console.error(\"Error en getDependencyStats:\", error);\n            return {\n                totalDependencies: 0,\n                totalTramites: 0,\n                totalOPAs: 0,\n                totalProcedures: 0,\n                averageProceduresPerDependency: 0\n            };\n        }\n    }\n    /**\n   * Busca dependencias por nombre\n   */ async searchDependencies(query) {\n        await this.initialize();\n        const searchTerm = query.toLowerCase().trim();\n        if (!searchTerm) return this.getAllDependencies();\n        return Array.from(this.dependencies.values()).filter((dep)=>dep.name.toLowerCase().includes(searchTerm) || dep.sigla.toLowerCase().includes(searchTerm) || dep.description && dep.description.toLowerCase().includes(searchTerm)).sort((a, b)=>b.totalProcedures - a.totalProcedures);\n    }\n    /**\n   * Obtiene procedimientos de una dependencia específica\n   */ async getProceduresByDependency(dependencyId) {\n        await this.initialize();\n        const dependency = this.dependencies.get(dependencyId);\n        if (!dependency) return null;\n        try {\n            // Primero obtener el UUID de la dependencia desde la base de datos\n            const { data: depData, error: depError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"dependencies\").select(\"id, name\").ilike(\"name\", \"%\".concat(dependency.name.replace(\"Secretar\\xeda\", \"Secretaria\"), \"%\")).single();\n            if (depError || !depData) {\n                console.error(\"Error obteniendo dependencia:\", depError);\n                return {\n                    dependency,\n                    tramites: [],\n                    opas: []\n                };\n            }\n            // Obtener trámites de esta dependencia desde Supabase\n            const { data: tramites, error: tramitesError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"procedures\").select(\"\\n          *,\\n          dependencies!inner(name, code)\\n        \").eq(\"dependency_id\", depData.id);\n            if (tramitesError) {\n                console.error(\"Error obteniendo tr\\xe1mites:\", tramitesError);\n            }\n            // Obtener OPAs de esta dependencia desde Supabase\n            const { data: opas, error: opasError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"opas\").select(\"\\n          *,\\n          dependencies!inner(name, code)\\n        \").eq(\"dependency_id\", depData.id);\n            if (opasError) {\n                console.error(\"Error obteniendo OPAs:\", opasError);\n            }\n            return {\n                dependency,\n                tramites: tramites || [],\n                opas: opas || []\n            };\n        } catch (error) {\n            console.error(\"Error obteniendo procedimientos por dependencia:\", error);\n            return {\n                dependency,\n                tramites: [],\n                opas: []\n            };\n        }\n    }\n    /**\n   * Obtiene las dependencias más populares (con más procedimientos)\n   */ async getTopDependencies() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 6;\n        await this.initialize();\n        return Array.from(this.dependencies.values()).sort((a, b)=>b.totalProcedures - a.totalProcedures).slice(0, limit);\n    }\n    constructor(){\n        this.dependencies = new Map();\n        this.initialized = false;\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (DependencyService.getInstance());\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/dependencyService.ts\n"));

/***/ })

});