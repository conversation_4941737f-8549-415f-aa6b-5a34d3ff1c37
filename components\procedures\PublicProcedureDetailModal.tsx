'use client'

import React, { useState } from 'react'
import { X, FileText, Clock, DollarSign, Building2, Phone, Mail, MapPin, ExternalLink, Download, CheckCircle, AlertCircle, Users, Calendar } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

interface Dependency {
  id: string
  name: string
  acronym?: string
  description?: string
  contact_email?: string
  contact_phone?: string
  address?: string
}

interface Procedure {
  id: string
  name: string
  description?: string
  cost?: number
  response_time?: string
  category?: string
  difficulty_level?: number
  popularity_score?: number
  requirements?: string[]
  documents_required?: string[]
  process_steps?: string[]
  legal_framework?: string
  contact_info?: any
  online_available?: boolean
  tags?: string[]
  dependency?: Dependency
}

interface PublicProcedureDetailModalProps {
  procedure: Procedure
  onClose: () => void
}

export function PublicProcedureDetailModal({ procedure, onClose }: PublicProcedureDetailModalProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'requirements' | 'process' | 'contact'>('overview')

  const formatCost = (cost?: number) => {
    if (!cost || cost === 0) return 'Gratuito'
    return `$${cost.toLocaleString('es-CO')}`
  }

  const getDifficultyLabel = (level?: number) => {
    if (!level) return 'No especificado'
    if (level <= 2) return 'Básico'
    if (level <= 4) return 'Intermedio'
    return 'Avanzado'
  }

  const getDifficultyColor = (level?: number) => {
    if (!level) return 'bg-gray-100 text-gray-800'
    if (level <= 2) return 'bg-green-100 text-green-800'
    if (level <= 4) return 'bg-yellow-100 text-yellow-800'
    return 'bg-red-100 text-red-800'
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-start p-6 border-b">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h2 className="text-2xl font-bold text-gray-900">
                {procedure.name}
              </h2>
              <Badge className="bg-chia-blue-100 text-chia-blue-800">
                Información
              </Badge>
            </div>
            
            {procedure.dependency && (
              <div className="flex items-center text-gray-600 mb-2">
                <Building2 className="h-4 w-4 mr-2" />
                <span>{procedure.dependency.name}</span>
                {procedure.dependency.acronym && (
                  <span className="ml-2 text-sm">({procedure.dependency.acronym})</span>
                )}
              </div>
            )}

            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center">
                <DollarSign className="h-4 w-4 mr-1" />
                <span>{formatCost(procedure.cost)}</span>
              </div>
              {procedure.response_time && (
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  <span>{procedure.response_time}</span>
                </div>
              )}
              {procedure.difficulty_level && (
                <Badge className={getDifficultyColor(procedure.difficulty_level)}>
                  {getDifficultyLabel(procedure.difficulty_level)}
                </Badge>
              )}
            </div>
          </div>
          
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Información General</TabsTrigger>
              <TabsTrigger value="requirements">Requisitos</TabsTrigger>
              <TabsTrigger value="process">Proceso</TabsTrigger>
              <TabsTrigger value="contact">Contacto</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Description */}
              {procedure.description && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Descripción</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700">{procedure.description}</p>
                  </CardContent>
                </Card>
              )}

              {/* Key Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-chia-blue-100 rounded-lg">
                        <DollarSign className="h-5 w-5 text-chia-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Costo</p>
                        <p className="font-semibold">{formatCost(procedure.cost)}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {procedure.response_time && (
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-chia-green-100 rounded-lg">
                          <Clock className="h-5 w-5 text-chia-green-600" />
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Tiempo de Respuesta</p>
                          <p className="font-semibold">{procedure.response_time}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {procedure.category && (
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-purple-100 rounded-lg">
                          <FileText className="h-5 w-5 text-purple-600" />
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Categoría</p>
                          <p className="font-semibold">{procedure.category}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {procedure.online_available && (
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-green-100 rounded-lg">
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Disponibilidad</p>
                          <p className="font-semibold">Disponible en línea</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* Tags */}
              {procedure.tags && procedure.tags.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Etiquetas</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {procedure.tags.map((tag, index) => (
                        <Badge key={index} variant="outline">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="requirements" className="space-y-6">
              {/* Requirements */}
              {procedure.requirements && procedure.requirements.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Requisitos</CardTitle>
                    <CardDescription>
                      Documentos y condiciones necesarias para realizar el trámite
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {procedure.requirements.map((requirement, index) => (
                        <li key={index} className="flex items-start space-x-2">
                          <CheckCircle className="h-4 w-4 text-chia-green-600 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{requirement}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}

              {/* Documents Required */}
              {procedure.documents_required && procedure.documents_required.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Documentos Requeridos</CardTitle>
                    <CardDescription>
                      Lista de documentos que debe presentar
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {procedure.documents_required.map((document, index) => (
                        <li key={index} className="flex items-start space-x-2">
                          <FileText className="h-4 w-4 text-chia-blue-600 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{document}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="process" className="space-y-6">
              {/* Process Steps */}
              {procedure.process_steps && procedure.process_steps.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Pasos del Proceso</CardTitle>
                    <CardDescription>
                      Sigue estos pasos para completar el trámite
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {procedure.process_steps.map((step, index) => (
                        <div key={index} className="flex items-start space-x-4">
                          <div className="flex-shrink-0 w-8 h-8 bg-chia-blue-100 text-chia-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">
                            {index + 1}
                          </div>
                          <div className="flex-1">
                            <p className="text-gray-700">{step}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Legal Framework */}
              {procedure.legal_framework && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Marco Legal</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700">{procedure.legal_framework}</p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="contact" className="space-y-6">
              {/* Dependency Contact */}
              {procedure.dependency && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Información de Contacto</CardTitle>
                    <CardDescription>
                      {procedure.dependency.name}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {procedure.dependency.contact_email && (
                      <div className="flex items-center space-x-3">
                        <Mail className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-600">Correo Electrónico</p>
                          <p className="font-medium">{procedure.dependency.contact_email}</p>
                        </div>
                      </div>
                    )}

                    {procedure.dependency.contact_phone && (
                      <div className="flex items-center space-x-3">
                        <Phone className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-600">Teléfono</p>
                          <p className="font-medium">{procedure.dependency.contact_phone}</p>
                        </div>
                      </div>
                    )}

                    {procedure.dependency.address && (
                      <div className="flex items-center space-x-3">
                        <MapPin className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-600">Dirección</p>
                          <p className="font-medium">{procedure.dependency.address}</p>
                        </div>
                      </div>
                    )}

                    {procedure.dependency.description && (
                      <div className="pt-4 border-t">
                        <p className="text-sm text-gray-600 mb-2">Descripción</p>
                        <p className="text-gray-700">{procedure.dependency.description}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Notice */}
              <Card className="bg-chia-blue-50 border-chia-blue-200">
                <CardContent className="p-4">
                  <div className="flex items-start space-x-3">
                    <AlertCircle className="h-5 w-5 text-chia-blue-600 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-chia-blue-900">
                        Para iniciar este trámite
                      </p>
                      <p className="text-sm text-chia-blue-700 mt-1">
                        Debes crear una cuenta en el sistema y autenticarte para poder iniciar y hacer seguimiento a tus trámites.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
