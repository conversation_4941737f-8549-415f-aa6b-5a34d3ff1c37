{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef689ce84c01-MIA", "connection": "close", "content-encoding": "br", "content-location": "/opas?dependency_id=eq.dd6ed28b-c972-4d6e-a994-1c9e4c937018&select=%2A", "content-profile": "public", "content-range": "0-38/39", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:50 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=l13DJc5AVWkNR_cAHDiPyiRam7pTfqcR50HDUA4nyW4-1751425670-*******-cNy8t3ZanBqhSLJXTattEVbZ6BqqczIvLBSTqwwlyfHzbkZOLdxn3UVLon7TM4eLcBTUN8wimTE1tt_dHmjx33SLBXcR9qADD76VG7jwfC4; path=/; expires=Wed, 02-Jul-25 03:37:50 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/opas?select=*&dependency_id=eq.dd6ed28b-c972-4d6e-a994-1c9e4c937018"}, "revalidate": 31536000, "tags": []}