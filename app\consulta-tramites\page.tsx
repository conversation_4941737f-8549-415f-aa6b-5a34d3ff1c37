import { createClient } from '@/lib/supabase/server'
import { PublicProcedureSearchInterface } from '@/components/procedures/PublicProcedureSearchInterface'
import { ContextualFAQSection } from '@/components/faq/ContextualFAQSection'

export default async function ConsultaTramitesPage() {
  const supabase = createClient()

  // Get all active procedures with their dependencies for public viewing
  const { data: procedures } = await supabase
    .from('procedures')
    .select(`
      *,
      dependency:dependencies(
        id,
        name,
        acronym,
        description,
        contact_email,
        contact_phone,
        address
      )
    `)
    .eq('is_active', true)
    .order('name', { ascending: true })

  // Get all dependencies for filtering
  const { data: dependencies } = await supabase
    .from('dependencies')
    .select('*')
    .eq('is_active', true)
    .order('name', { ascending: true })

  // Get procedure categories
  const { data: categoriesData } = await supabase
    .from('procedures')
    .select('category')
    .not('category', 'is', null)
    .eq('is_active', true)

  const categories = [...new Set(categoriesData?.map(p => p.category).filter(Boolean))]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-chia-blue-600 to-chia-blue-800 text-white shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-8">
            <div>
              <h1 className="text-3xl font-bold">
                Consulta de Trámites y OPAs
              </h1>
              <p className="text-blue-100 mt-1">
                Consulta información detallada sobre trámites y otros procedimientos administrativos municipales
              </p>
              <div className="flex items-center mt-3 space-x-4 text-sm">
                <div className="flex items-center">
                  <span className="font-medium">{procedures?.length || 0}</span>
                  <span className="ml-1">procedimientos disponibles</span>
                </div>
                <div className="flex items-center">
                  <span className="font-medium">{dependencies?.length || 0}</span>
                  <span className="ml-1">dependencias</span>
                </div>
                <div className="flex items-center">
                  <span className="font-medium">{categories.length}</span>
                  <span className="ml-1">categorías</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <PublicProcedureSearchInterface
          procedures={procedures || []}
          dependencies={dependencies || []}
          categories={categories}
        />
      </div>

      {/* FAQ Section - Contextual for procedure consultation */}
      <div className="bg-white border-t">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <ContextualFAQSection context="procedures" />
        </div>
      </div>
    </div>
  )
}
