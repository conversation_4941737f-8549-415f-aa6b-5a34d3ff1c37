{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef824b3c498e-MIA", "connection": "close", "content-encoding": "br", "content-location": "/opas?dependency_id=eq.12c41975-e14d-49ea-9d1e-4d771dd6936c&select=%2A", "content-profile": "public", "content-range": "0-44/45", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:54 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=aDsRs8rHDCsfW6P8Mv.WG0Ref9PeLZGSqCcMfT5eifk-1751425674-1.0.1.1-5ZEQ3fjWkLzzXcjtaacWHkhIT_tTqm354uxc_jCcCB0yt4iEBtTQyARxxoYPykAAVasna88cdHV4Xz0VVh5JNKHZGEzNv9ldPGcVCz__j0I; path=/; expires=Wed, 02-Jul-25 03:37:54 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/opas?select=*&dependency_id=eq.12c41975-e14d-49ea-9d1e-4d771dd6936c"}, "revalidate": 31536000, "tags": []}