{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef9519964988-MIA", "connection": "close", "content-encoding": "br", "content-location": "/procedures?dependency_id=eq.2cbdf943-b9df-496c-b18f-9bb5129c506a&select=%2A", "content-profile": "public", "content-range": "*/0", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:57 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=SBoP0xGfhb2hjmwejTxGRR5OB5NaYtxlM15qKRA1veM-1751425677-1.0.1.1-9nawskdhxNE6tIGXwaWhBfJYzdPm6YMbAUdoqHRu2yVxgs105gqu11LIwylyPwqwexI.Do3JG_MppZoCt0zqHy.XKBQ8NDyjLFxNP_CpR2g; path=/; expires=Wed, 02-Jul-25 03:37:57 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "1"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/procedures?select=*&dependency_id=eq.2cbdf943-b9df-496c-b18f-9bb5129c506a"}, "revalidate": 31536000, "tags": []}