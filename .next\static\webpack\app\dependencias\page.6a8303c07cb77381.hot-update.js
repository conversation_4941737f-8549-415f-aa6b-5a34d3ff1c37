"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dependencias/page",{

/***/ "(app-pages-browser)/./lib/services/dependencyService.ts":
/*!*******************************************!*\
  !*** ./lib/services/dependencyService.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./lib/supabase/client.ts\");\n\n/**\n * Servicio para gestionar datos de dependencias municipales\n * Procesa información de trámites y OPAs agrupados por dependencia\n */ class DependencyService {\n    static getInstance() {\n        if (!DependencyService.instance) {\n            DependencyService.instance = new DependencyService();\n        }\n        return DependencyService.instance;\n    }\n    /**\n   * Inicializa el servicio con las 14 dependencias oficiales del municipio\n   */ async initialize() {\n        if (this.initialized) return;\n        try {\n            // Usar las dependencias oficiales en lugar de procesar datos dinámicamente\n            await this.createOfficialDependencies();\n            this.initialized = true;\n        } catch (error) {\n            console.error(\"Error inicializando DependencyService:\", error);\n            // Fallback: usar el método anterior si falla\n            try {\n                await this.processDependencies();\n                this.initialized = true;\n            } catch (fallbackError) {\n                console.error(\"Error en fallback:\", fallbackError);\n                throw error;\n            }\n        }\n    }\n    /**\n   * Procesa los datos de trámites y OPAs para extraer información de dependencias\n   */ async processDependencies() {\n        const dependencyMap = new Map();\n        try {\n            // Obtener trámites desde Supabase\n            const { data: tramitesData, error: tramitesError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"procedures\").select(\"*\");\n            if (tramitesError) {\n                console.error(\"Error obteniendo tr\\xe1mites:\", tramitesError);\n                throw tramitesError;\n            }\n            // Procesar trámites\n            tramitesData === null || tramitesData === void 0 ? void 0 : tramitesData.forEach((tramite)=>{\n                const depCode = tramite.dependency_code || tramite.codigo_dependencia || \"unknown\";\n                const depName = tramite.dependency_name || tramite.dependencia || \"Dependencia Desconocida\";\n                if (!dependencyMap.has(depCode)) {\n                    dependencyMap.set(depCode, {\n                        name: depName,\n                        sigla: this.extractSigla(depName),\n                        tramites: [],\n                        opas: [],\n                        subdependenciasCount: 0\n                    });\n                }\n                dependencyMap.get(depCode).tramites.push(tramite);\n            });\n            // Obtener OPAs desde Supabase\n            const { data: opasData, error: opasError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"opas\").select(\"*\");\n            if (opasError) {\n                console.error(\"Error obteniendo OPAs:\", opasError);\n                throw opasError;\n            }\n            // Procesar OPAs\n            opasData === null || opasData === void 0 ? void 0 : opasData.forEach((opa)=>{\n                const depCode = opa.dependency_code || opa.codigo_dependencia || \"unknown\";\n                const depName = opa.dependency_name || opa.dependencia || \"Dependencia Desconocida\";\n                if (!dependencyMap.has(depCode)) {\n                    dependencyMap.set(depCode, {\n                        name: depName,\n                        sigla: this.extractSigla(depName),\n                        tramites: [],\n                        opas: [],\n                        subdependenciasCount: 0\n                    });\n                }\n                dependencyMap.get(depCode).opas.push(opa);\n            });\n        } catch (error) {\n            console.error(\"Error procesando dependencias:\", error);\n            throw error;\n        }\n        // Convertir a formato Dependency\n        dependencyMap.forEach((depData, depCode)=>{\n            const dependency = {\n                id: depCode,\n                name: depData.name,\n                sigla: depData.sigla,\n                tramitesCount: depData.tramites.length,\n                opasCount: depData.opas.length,\n                totalProcedures: depData.tramites.length + depData.opas.length,\n                subdependenciasCount: depData.subdependenciasCount || 0,\n                description: this.generateDescription(depData.name),\n                icon: this.assignIcon(depData.name),\n                color: this.assignColor(depCode)\n            };\n            this.dependencies.set(depCode, dependency);\n        });\n    }\n    /**\n   * Crear las 14 dependencias oficiales del municipio de Chía\n   */ async createOfficialDependencies() {\n        // Definir las 14 dependencias oficiales del municipio de Chía\n        const officialDependencies = [\n            {\n                id: \"despacho-alcalde\",\n                name: \"Despacho del Alcalde\",\n                sigla: \"DA\"\n            },\n            {\n                id: \"secretaria-general\",\n                name: \"Secretar\\xeda General\",\n                sigla: \"SG\"\n            },\n            {\n                id: \"secretaria-gobierno\",\n                name: \"Secretar\\xeda de Gobierno y Convivencia\",\n                sigla: \"SGC\"\n            },\n            {\n                id: \"secretaria-hacienda\",\n                name: \"Secretar\\xeda de Hacienda\",\n                sigla: \"SH\"\n            },\n            {\n                id: \"secretaria-planeacion\",\n                name: \"Secretar\\xeda de Planeaci\\xf3n\",\n                sigla: \"SP\"\n            },\n            {\n                id: \"secretaria-desarrollo-social\",\n                name: \"Secretar\\xeda de Desarrollo Social\",\n                sigla: \"SDS\"\n            },\n            {\n                id: \"secretaria-educacion\",\n                name: \"Secretar\\xeda de Educaci\\xf3n\",\n                sigla: \"SE\"\n            },\n            {\n                id: \"secretaria-salud\",\n                name: \"Secretar\\xeda de Salud\",\n                sigla: \"SS\"\n            },\n            {\n                id: \"secretaria-movilidad\",\n                name: \"Secretar\\xeda de Movilidad\",\n                sigla: \"SM\"\n            },\n            {\n                id: \"secretaria-desarrollo-economico\",\n                name: \"Secretar\\xeda de Desarrollo Econ\\xf3mico\",\n                sigla: \"SDE\"\n            },\n            {\n                id: \"secretaria-infraestructura\",\n                name: \"Secretar\\xeda de Infraestructura\",\n                sigla: \"SI\"\n            },\n            {\n                id: \"secretaria-ambiente\",\n                name: \"Secretar\\xeda de Ambiente\",\n                sigla: \"SA\"\n            },\n            {\n                id: \"secretaria-tecnologias\",\n                name: \"Secretar\\xeda de Tecnolog\\xedas de la Informaci\\xf3n\",\n                sigla: \"STI\"\n            },\n            {\n                id: \"entidades-descentralizadas\",\n                name: \"Entidades Descentralizadas\",\n                sigla: \"ED\"\n            }\n        ];\n        // Obtener contadores reales de trámites y OPAs\n        const counters = await this.getDependencyCounters();\n        // Crear cada dependencia oficial\n        officialDependencies.forEach((depInfo, index)=>{\n            const counter = counters.get(depInfo.name) || {\n                tramites: 0,\n                opas: 0,\n                subdependencies: 0\n            };\n            const dependency = {\n                id: depInfo.id,\n                name: depInfo.name,\n                sigla: depInfo.sigla,\n                tramitesCount: counter.tramites,\n                opasCount: counter.opas,\n                totalProcedures: counter.tramites + counter.opas,\n                subdependenciasCount: counter.subdependencies,\n                description: this.generateDescription(depInfo.name),\n                icon: this.assignIcon(depInfo.name),\n                color: this.assignColor(depInfo.id)\n            };\n            this.dependencies.set(depInfo.id, dependency);\n        });\n    }\n    /**\n   * Obtener contadores de trámites y OPAs por dependencia\n   */ async getDependencyCounters() {\n        const counters = new Map();\n        try {\n            var // Procesar trámites\n            _tramitesResult_data, // Procesar OPAs\n            _opasResult_data;\n            // Obtener datos de Supabase con JOIN para obtener nombres de dependencias\n            const [tramitesResult, opasResult] = await Promise.all([\n                _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"procedures\").select(\"\\n            id,\\n            dependencies!inner(name, code)\\n          \"),\n                _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"opas\").select(\"\\n            id,\\n            dependencies!inner(name, code)\\n          \")\n            ]);\n            (_tramitesResult_data = tramitesResult.data) === null || _tramitesResult_data === void 0 ? void 0 : _tramitesResult_data.forEach((item)=>{\n                var _item_dependencies;\n                const depName = this.mapToOfficialDependency((_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.name);\n                if (!counters.has(depName)) {\n                    counters.set(depName, {\n                        tramites: 0,\n                        opas: 0,\n                        subdependencies: 0\n                    });\n                }\n                counters.get(depName).tramites++;\n            });\n            (_opasResult_data = opasResult.data) === null || _opasResult_data === void 0 ? void 0 : _opasResult_data.forEach((item)=>{\n                var _item_dependencies;\n                const depName = this.mapToOfficialDependency((_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.name);\n                if (!counters.has(depName)) {\n                    counters.set(depName, {\n                        tramites: 0,\n                        opas: 0,\n                        subdependencies: 0\n                    });\n                }\n                counters.get(depName).opas++;\n            });\n        } catch (error) {\n            console.error(\"Error obteniendo contadores:\", error);\n        }\n        return counters;\n    }\n    /**\n   * Mapear nombres de dependencias de los datos a las dependencias oficiales\n   */ mapToOfficialDependency(originalName) {\n        if (!originalName) return \"Entidades Descentralizadas\";\n        const mappings = {\n            \"Despacho Alcalde\": \"Despacho del Alcalde\",\n            \"Secretaria General\": \"Secretar\\xeda General\",\n            \"Secretaria de Gobierno y Convivencia\": \"Secretar\\xeda de Gobierno y Convivencia\",\n            \"Secretaria de Hacienda\": \"Secretar\\xeda de Hacienda\",\n            \"Secretaria de Planeacion\": \"Secretar\\xeda de Planeaci\\xf3n\",\n            \"Secretaria de Desarrollo Social\": \"Secretar\\xeda de Desarrollo Social\",\n            \"Secretaria de Educacion\": \"Secretar\\xeda de Educaci\\xf3n\",\n            \"Secretaria de Salud\": \"Secretar\\xeda de Salud\",\n            \"Secretaria de Movilidad\": \"Secretar\\xeda de Movilidad\",\n            \"Secretaria para el Desarrollo Economico\": \"Secretar\\xeda de Desarrollo Econ\\xf3mico\",\n            \"Secretaria de Infraestructura\": \"Secretar\\xeda de Infraestructura\",\n            \"Secretaria de Medio Ambiente\": \"Secretar\\xeda de Ambiente\",\n            \"Secretaria de Participacion Ciudadana y Accion Comunitaria\": \"Secretar\\xeda de Gobierno y Convivencia\",\n            \"Descentralizados\": \"Entidades Descentralizadas\"\n        };\n        return mappings[originalName] || originalName;\n    }\n    /**\n   * Extrae sigla de un nombre de dependencia\n   */ extractSigla(name) {\n        if (name.includes(\"Secretar\\xeda\")) {\n            const words = name.replace(\"Secretar\\xeda de \", \"\").split(\" \");\n            return words.map((word)=>word.charAt(0).toUpperCase()).join(\"\");\n        }\n        const words = name.split(\" \");\n        if (words.length === 1) return words[0].substring(0, 3).toUpperCase();\n        return words.map((word)=>word.charAt(0).toUpperCase()).join(\"\").substring(0, 4);\n    }\n    /**\n   * Genera descripción para una dependencia\n   */ generateDescription(name) {\n        const descriptions = {\n            \"Despacho del Alcalde\": \"Oficina principal del Alcalde Municipal y coordinaci\\xf3n general\",\n            \"Secretar\\xeda General\": \"Gesti\\xf3n administrativa, jur\\xeddica y de archivo municipal\",\n            \"Secretar\\xeda de Gobierno y Convivencia\": \"Orden p\\xfablico, convivencia ciudadana y participaci\\xf3n comunitaria\",\n            \"Secretar\\xeda de Hacienda\": \"Administraci\\xf3n financiera, tributaria y presupuestal municipal\",\n            \"Secretar\\xeda de Planeaci\\xf3n\": \"Ordenamiento territorial, desarrollo urbano y gesti\\xf3n de proyectos\",\n            \"Secretar\\xeda de Desarrollo Social\": \"Programas sociales, atenci\\xf3n a poblaci\\xf3n vulnerable y desarrollo comunitario\",\n            \"Secretar\\xeda de Educaci\\xf3n\": \"Administraci\\xf3n del sistema educativo municipal y programas pedag\\xf3gicos\",\n            \"Secretar\\xeda de Salud\": \"Vigilancia epidemiol\\xf3gica, control sanitario y salud p\\xfablica\",\n            \"Secretar\\xeda de Movilidad\": \"Gesti\\xf3n del tr\\xe1nsito, transporte p\\xfablico y movilidad sostenible\",\n            \"Secretar\\xeda de Desarrollo Econ\\xf3mico\": \"Promoci\\xf3n empresarial, turismo y desarrollo econ\\xf3mico local\",\n            \"Secretar\\xeda de Infraestructura\": \"Obras p\\xfablicas, mantenimiento vial y proyectos de infraestructura\",\n            \"Secretar\\xeda de Ambiente\": \"Gesti\\xf3n ambiental, recursos naturales y sostenibilidad\",\n            \"Secretar\\xeda de Tecnolog\\xedas de la Informaci\\xf3n\": \"Sistemas de informaci\\xf3n, gobierno digital y tecnolog\\xeda\",\n            \"Entidades Descentralizadas\": \"Empresas de servicios p\\xfablicos y entidades adscritas al municipio\"\n        };\n        return descriptions[name] || \"Gesti\\xf3n de procedimientos administrativos de \".concat(name);\n    }\n    /**\n   * Asigna icono representativo para una dependencia\n   */ assignIcon(name) {\n        const icons = {\n            \"Despacho del Alcalde\": \"crown\",\n            \"Secretar\\xeda General\": \"folder\",\n            \"Secretar\\xeda de Gobierno y Convivencia\": \"shield\",\n            \"Secretar\\xeda de Hacienda\": \"banknote\",\n            \"Secretar\\xeda de Planeaci\\xf3n\": \"map\",\n            \"Secretar\\xeda de Desarrollo Social\": \"users\",\n            \"Secretar\\xeda de Educaci\\xf3n\": \"graduation-cap\",\n            \"Secretar\\xeda de Salud\": \"heart-pulse\",\n            \"Secretar\\xeda de Movilidad\": \"car\",\n            \"Secretar\\xeda de Desarrollo Econ\\xf3mico\": \"building\",\n            \"Secretar\\xeda de Infraestructura\": \"building\",\n            \"Secretar\\xeda de Ambiente\": \"folder\",\n            \"Secretar\\xeda de Tecnolog\\xedas de la Informaci\\xf3n\": \"folder\",\n            \"Entidades Descentralizadas\": \"building\"\n        };\n        return icons[name] || \"folder\";\n    }\n    /**\n   * Asigna color temático para una dependencia\n   */ assignColor(depCode) {\n        const colors = [\n            \"bg-chia-blue-100 border-chia-blue-300 hover:bg-chia-blue-200\",\n            \"bg-chia-green-100 border-chia-green-300 hover:bg-chia-green-200\",\n            \"bg-blue-100 border-blue-300 hover:bg-blue-200\",\n            \"bg-green-100 border-green-300 hover:bg-green-200\",\n            \"bg-purple-100 border-purple-300 hover:bg-purple-200\",\n            \"bg-orange-100 border-orange-300 hover:bg-orange-200\",\n            \"bg-teal-100 border-teal-300 hover:bg-teal-200\",\n            \"bg-indigo-100 border-indigo-300 hover:bg-indigo-200\",\n            \"bg-pink-100 border-pink-300 hover:bg-pink-200\"\n        ];\n        const index = parseInt(depCode) % colors.length;\n        return colors[index];\n    }\n    /**\n   * Obtiene todas las dependencias directamente desde la base de datos\n   */ async getAllDependencies() {\n        try {\n            // Obtener dependencias con contadores desde la base de datos\n            const { data: dependenciesData, error: dependenciesError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"dependencies\").select(\"id, name, code, acronym\").order(\"name\");\n            if (dependenciesError) {\n                console.error(\"Error obteniendo dependencias:\", dependenciesError);\n                return [];\n            }\n            // Obtener contadores de trámites y OPAs para cada dependencia\n            const dependencies = [];\n            for (const dep of dependenciesData || []){\n                // Contar trámites\n                const { count: tramitesCount } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"procedures\").select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"dependency_id\", dep.id);\n                // Contar OPAs\n                const { count: opasCount } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"opas\").select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"dependency_id\", dep.id);\n                const dependency = {\n                    id: dep.id,\n                    name: dep.name,\n                    sigla: dep.acronym || dep.code || \"\",\n                    tramitesCount: tramitesCount || 0,\n                    opasCount: opasCount || 0,\n                    totalProcedures: (tramitesCount || 0) + (opasCount || 0),\n                    subdependenciasCount: 0,\n                    description: this.generateDescription(dep.name),\n                    icon: this.assignIcon(dep.name),\n                    color: this.assignColor(dep.id)\n                };\n                dependencies.push(dependency);\n            }\n            return dependencies.sort((a, b)=>b.totalProcedures - a.totalProcedures);\n        } catch (error) {\n            console.error(\"Error en getAllDependencies:\", error);\n            return [];\n        }\n    }\n    /**\n   * Obtiene una dependencia por ID\n   */ async getDependencyById(id) {\n        await this.initialize();\n        return this.dependencies.get(id) || null;\n    }\n    /**\n   * Obtiene estadísticas generales de dependencias directamente desde la base de datos\n   */ async getDependencyStats() {\n        try {\n            // Contar dependencias\n            const { count: totalDependencies } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"dependencies\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            });\n            // Contar trámites\n            const { count: totalTramites } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"procedures\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            });\n            // Contar OPAs\n            const { count: totalOPAs } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"opas\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            });\n            const totalProcedures = (totalTramites || 0) + (totalOPAs || 0);\n            const averageProceduresPerDependency = totalDependencies > 0 ? Math.round(totalProcedures / totalDependencies) : 0;\n            return {\n                totalDependencies: totalDependencies || 0,\n                totalTramites: totalTramites || 0,\n                totalOPAs: totalOPAs || 0,\n                totalProcedures,\n                averageProceduresPerDependency\n            };\n        } catch (error) {\n            console.error(\"Error en getDependencyStats:\", error);\n            return {\n                totalDependencies: 0,\n                totalTramites: 0,\n                totalOPAs: 0,\n                totalProcedures: 0,\n                averageProceduresPerDependency: 0\n            };\n        }\n    }\n    /**\n   * Busca dependencias por nombre\n   */ async searchDependencies(query) {\n        await this.initialize();\n        const searchTerm = query.toLowerCase().trim();\n        if (!searchTerm) return this.getAllDependencies();\n        return Array.from(this.dependencies.values()).filter((dep)=>dep.name.toLowerCase().includes(searchTerm) || dep.sigla.toLowerCase().includes(searchTerm) || dep.description && dep.description.toLowerCase().includes(searchTerm)).sort((a, b)=>b.totalProcedures - a.totalProcedures);\n    }\n    /**\n   * Obtiene procedimientos de una dependencia específica\n   */ async getProceduresByDependency(dependencyId) {\n        await this.initialize();\n        const dependency = this.dependencies.get(dependencyId);\n        if (!dependency) return null;\n        try {\n            // Primero obtener el UUID de la dependencia desde la base de datos\n            const { data: depData, error: depError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"dependencies\").select(\"id, name\").ilike(\"name\", \"%\".concat(dependency.name.replace(\"Secretar\\xeda\", \"Secretaria\"), \"%\")).single();\n            if (depError || !depData) {\n                console.error(\"Error obteniendo dependencia:\", depError);\n                return {\n                    dependency,\n                    tramites: [],\n                    opas: []\n                };\n            }\n            // Obtener trámites de esta dependencia desde Supabase\n            const { data: tramites, error: tramitesError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"procedures\").select(\"\\n          *,\\n          dependencies!inner(name, code)\\n        \").eq(\"dependency_id\", depData.id);\n            if (tramitesError) {\n                console.error(\"Error obteniendo tr\\xe1mites:\", tramitesError);\n            }\n            // Obtener OPAs de esta dependencia desde Supabase\n            const { data: opas, error: opasError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"opas\").select(\"\\n          *,\\n          dependencies!inner(name, code)\\n        \").eq(\"dependency_id\", depData.id);\n            if (opasError) {\n                console.error(\"Error obteniendo OPAs:\", opasError);\n            }\n            return {\n                dependency,\n                tramites: tramites || [],\n                opas: opas || []\n            };\n        } catch (error) {\n            console.error(\"Error obteniendo procedimientos por dependencia:\", error);\n            return {\n                dependency,\n                tramites: [],\n                opas: []\n            };\n        }\n    }\n    /**\n   * Obtiene las dependencias más populares (con más procedimientos)\n   */ async getTopDependencies() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 6;\n        await this.initialize();\n        return Array.from(this.dependencies.values()).sort((a, b)=>b.totalProcedures - a.totalProcedures).slice(0, limit);\n    }\n    constructor(){\n        this.dependencies = new Map();\n        this.initialized = false;\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (DependencyService.getInstance());\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/dependencyService.ts\n"));

/***/ })

});