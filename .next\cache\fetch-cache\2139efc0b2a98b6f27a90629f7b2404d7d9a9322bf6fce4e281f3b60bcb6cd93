{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef845d834c0f-MIA", "connection": "close", "content-encoding": "br", "content-location": "/procedures?dependency_id=eq.a716252f-8089-4381-9787-d3f05a85bb76&select=%2A", "content-profile": "public", "content-range": "*/0", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:54 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=aMw7GJ4Bv90fnRnRyJxDZ0vwmTvRCky8Y1NAhrv3DdM-1751425674-1.0.1.1-lwUtpGZmKX1jHFBuge.E4bHq7xF0qioJpXwqQn5KbltwUeKT5TmWCI1NrI_nF0ORwMKqq9US82ytUHmQsgnIc2o0YyYnECbbwL40_lFgOwQ; path=/; expires=Wed, 02-Jul-25 03:37:54 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/procedures?select=*&dependency_id=eq.a716252f-8089-4381-9787-d3f05a85bb76"}, "revalidate": 31536000, "tags": []}