{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958b10dc4bd7da4f-MIA", "connection": "close", "content-encoding": "br", "content-location": "/subdependencies?dependency_id=eq.6c51c8c2-5543-4200-a0f7-be231dee9553&is_active=eq.true&select=%2A", "content-profile": "public", "content-range": "0-3/4", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:30:40 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=HT7J6Ro7IRpu9zUCo93ieq1xSrIfrsEOBy6V.F6jfdY-1751427040-1.0.1.1-yVYMFV79e92P5SJSmltXOqJE5AEaGLuasHjzst7yiwqsb9RQ5zEk5AxUSOyEG.TjeKAGxumxKnKiOiLm2UhAYvGO3e.0.PtjGoiSE2NLL6k; path=/; expires=Wed, 02-Jul-25 04:00:40 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/subdependencies?select=*&dependency_id=eq.6c51c8c2-5543-4200-a0f7-be231dee9553&is_active=eq.true"}, "revalidate": 31536000, "tags": []}