{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef7bfb91ed34-MIA", "connection": "close", "content-encoding": "br", "content-location": "/procedures?dependency_id=eq.be4eb6dd-b058-4f3c-a221-c88257335c0b&select=%2A", "content-profile": "public", "content-range": "0-7/8", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:53 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=kT0H21cnbezONc9lLxlfiNac7HyWVG3svLpkPBkH_Ss-1751425673-1.0.1.1-OhRmEPaaQ_cRGtEc1NDgFaoKbAZGh_IRVf8JE2KbsHnt323s5sMSwhbnPmIWtNlbe3mVoFtPaImdgqaWEOlJ72zq5ABZH_K7CgaWrgac5A0; path=/; expires=Wed, 02-Jul-25 03:37:53 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/procedures?select=*&dependency_id=eq.be4eb6dd-b058-4f3c-a221-c88257335c0b"}, "revalidate": 31536000, "tags": []}