{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958b10f3a80adb3f-BOG", "connection": "close", "content-encoding": "br", "content-location": "/subdependencies?dependency_id=eq.2a8052a4-2d59-4b68-a7fc-df811e381801&is_active=eq.true&select=%2A", "content-profile": "public", "content-range": "0-2/3", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:30:44 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=GyZf0UKL5ZvYturIOhw2G8p2TzvG6zxgxI_2FObpGTM-1751427044-1.0.1.1-dIXvzwlgeh4nmXT_CQ.pVnvOasdMKfINLWNn_kZtk9Y2gf6MqQsQ9gUIbabhwDdX0TTHXt6q926UoEJPyeR4I57V5EcuEa0h7vgT8j6TNCs; path=/; expires=Wed, 02-Jul-25 04:00:44 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/subdependencies?select=*&dependency_id=eq.2a8052a4-2d59-4b68-a7fc-df811e381801&is_active=eq.true"}, "revalidate": 31536000, "tags": []}