{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958b10e0bb4e4c1a-MIA", "connection": "close", "content-encoding": "br", "content-location": "/subdependencies?dependency_id=eq.be4eb6dd-b058-4f3c-a221-c88257335c0b&is_active=eq.true&select=%2A", "content-profile": "public", "content-range": "0-3/4", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:30:41 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=s7gk4YoUsEo530UaOKjfFjmT5K3X_3CckTuOQii.mOg-1751427041-1.0.1.1-r4dVY9hggivIqu.5nwAOxbGeMoIbqWXIGwQ2dqvjHl1OZ0BN0FrrdX_Kwt9rWN6qdp55CBPOeygFRpKApt1GR2lpY0TcurxplrFyRUuGpjs; path=/; expires=Wed, 02-Jul-25 04:00:41 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/subdependencies?select=*&dependency_id=eq.be4eb6dd-b058-4f3c-a221-c88257335c0b&is_active=eq.true"}, "revalidate": 31536000, "tags": []}