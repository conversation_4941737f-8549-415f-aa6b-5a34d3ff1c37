{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef907f604c1c-MIA", "connection": "close", "content-encoding": "br", "content-location": "/opas?dependency_id=eq.8525824e-448e-470d-9911-69c19ad65bc8&select=%2A", "content-profile": "public", "content-range": "0-56/57", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:56 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=RTf8n_5p2Af77_sd27h075SUYxreKiClKT_sJZdCBdY-1751425676-1.0.1.1-U5bZiEhc9sUf8gdhRH93uPd8eQgF6h3fUYS6w0FLw6AbXcTzdvaEqGS6rX6ncFZOF1lH5nboTVZP5DIzzO.q5uXvS95M8GM9kLhQJyD_LEw; path=/; expires=Wed, 02-Jul-25 03:37:56 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "4"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/opas?select=*&dependency_id=eq.8525824e-448e-470d-9911-69c19ad65bc8"}, "revalidate": 31536000, "tags": []}