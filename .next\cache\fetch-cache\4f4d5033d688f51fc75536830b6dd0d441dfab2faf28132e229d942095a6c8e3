{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958b10e6daa74c1b-MIA", "connection": "close", "content-encoding": "br", "content-location": "/subdependencies?dependency_id=eq.d3aaa47f-9f94-4574-9ba1-79052e891ba6&is_active=eq.true&select=%2A", "content-profile": "public", "content-range": "0-5/6", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:30:42 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=UdC9KC6RVfeZvvftgxDnL1C4byYBQ9idgj8eVy3stLs-1751427042-1.0.1.1-cgL9VmTrPau5JKOIGsMe0.VXLka333B4tJ9JSE0nZK58sparzs7gIvJVKqv_6ClcCKN0bbqhI4WtNXYZ_9qXWLvqn_ia4JxpJT.xnWNUs_U; path=/; expires=Wed, 02-Jul-25 04:00:42 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/subdependencies?select=*&dependency_id=eq.d3aaa47f-9f94-4574-9ba1-79052e891ba6&is_active=eq.true"}, "revalidate": 31536000, "tags": []}