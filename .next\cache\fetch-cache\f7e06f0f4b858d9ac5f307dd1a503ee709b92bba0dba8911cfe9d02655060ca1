{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef99df6c3dd7-MIA", "connection": "close", "content-encoding": "br", "content-location": "/procedures?dependency_id=eq.2a8052a4-2d59-4b68-a7fc-df811e381801&select=%2A", "content-profile": "public", "content-range": "0-0/1", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:58 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=yPa4gsw2tnrHvqGzOpRnTJDgLfhLzgNjnY9OCwwgYow-1751425678-1.0.1.1-q377flSB7hzp5tIHpnP_X6X28ycqxGRgCnq780bnXv0py7Kc49agG.bZFAvSk45u5sgvMpVwKszqzEK8PLqYGLk2LtjhnQckiCcpxSzehB4; path=/; expires=Wed, 02-Jul-25 03:37:58 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/procedures?select=*&dependency_id=eq.2a8052a4-2d59-4b68-a7fc-df811e381801"}, "revalidate": 31536000, "tags": []}