{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958b10d3bf74db3f-BOG", "connection": "close", "content-encoding": "br", "content-location": "/subdependencies?dependency_id=eq.dd6ed28b-c972-4d6e-a994-1c9e4c937018&is_active=eq.true&select=%2A", "content-profile": "public", "content-range": "0-3/4", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:30:39 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=NPY3s.Q4F.HLXRqjnMQXSNa0d03oFK44NESl5zYBz6w-1751427039-1.0.1.1-6E9xxv8ON2IBHEXwCSrlpNlC1PlVn70FWzfjI3KEXlax7IqtgSIwxO_xEv6UXYIQ8vIWyGgV8WyKc.UgMsDI_DazdrIrmA.AJ5nLjIWdYHo; path=/; expires=Wed, 02-Jul-25 04:00:39 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "15"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/subdependencies?select=*&dependency_id=eq.dd6ed28b-c972-4d6e-a994-1c9e4c937018&is_active=eq.true"}, "revalidate": 31536000, "tags": []}