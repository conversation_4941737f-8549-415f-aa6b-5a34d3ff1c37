{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef6af812497c-MIA", "connection": "close", "content-encoding": "br", "content-location": "/procedures?dependency_id=eq.c7c5b677-0a1f-4b82-be62-b3feb55285ae&select=%2A", "content-profile": "public", "content-range": "0-28/29", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:50 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=VgwDeIluywCMuRbK9JmGxQjvFYcwWKip0pMF7RdDEJE-1751425670-*******-QyToQztlWECOXS.ADXsxt2vCOpgNGmPjLLToSBfzKHxFIRl2JXvdrhXr4L5WYZ1DONjsUIWS.AMa_5d5nUmptsrD61bt0jsVeu.P9jkf4RU; path=/; expires=Wed, 02-Jul-25 03:37:50 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/procedures?select=*&dependency_id=eq.c7c5b677-0a1f-4b82-be62-b3feb55285ae"}, "revalidate": 31536000, "tags": []}