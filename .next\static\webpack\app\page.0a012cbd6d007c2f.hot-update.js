"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/services/dependencyService.ts":
/*!*******************************************!*\
  !*** ./lib/services/dependencyService.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./lib/supabase/client.ts\");\n\n/**\n * Servicio para gestionar datos de dependencias municipales\n * Procesa información de trámites y OPAs agrupados por dependencia\n */ class DependencyService {\n    static getInstance() {\n        if (!DependencyService.instance) {\n            DependencyService.instance = new DependencyService();\n        }\n        return DependencyService.instance;\n    }\n    /**\n   * Inicializa el servicio con las 14 dependencias oficiales del municipio\n   */ async initialize() {\n        if (this.initialized) return;\n        try {\n            // Usar las dependencias oficiales en lugar de procesar datos dinámicamente\n            await this.createOfficialDependencies();\n            this.initialized = true;\n        } catch (error) {\n            console.error(\"Error inicializando DependencyService:\", error);\n            // Fallback: usar el método anterior si falla\n            try {\n                await this.processDependencies();\n                this.initialized = true;\n            } catch (fallbackError) {\n                console.error(\"Error en fallback:\", fallbackError);\n                throw error;\n            }\n        }\n    }\n    /**\n   * Procesa los datos de trámites y OPAs para extraer información de dependencias\n   */ async processDependencies() {\n        const dependencyMap = new Map();\n        try {\n            // Obtener trámites desde Supabase\n            const { data: tramitesData, error: tramitesError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"procedures\").select(\"*\");\n            if (tramitesError) {\n                console.error(\"Error obteniendo tr\\xe1mites:\", tramitesError);\n                throw tramitesError;\n            }\n            // Procesar trámites\n            tramitesData === null || tramitesData === void 0 ? void 0 : tramitesData.forEach((tramite)=>{\n                const depCode = tramite.dependency_code || tramite.codigo_dependencia || \"unknown\";\n                const depName = tramite.dependency_name || tramite.dependencia || \"Dependencia Desconocida\";\n                if (!dependencyMap.has(depCode)) {\n                    dependencyMap.set(depCode, {\n                        name: depName,\n                        sigla: this.extractSigla(depName),\n                        tramites: [],\n                        opas: [],\n                        subdependenciasCount: 0 // Se calculará después\n                    });\n                }\n                dependencyMap.get(depCode).tramites.push(tramite);\n            });\n            // Obtener OPAs desde Supabase\n            const { data: opasData, error: opasError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"opas\").select(\"*\");\n            if (opasError) {\n                console.error(\"Error obteniendo OPAs:\", opasError);\n                throw opasError;\n            }\n            // Procesar OPAs\n            opasData === null || opasData === void 0 ? void 0 : opasData.forEach((opa)=>{\n                const depCode = opa.dependency_code || opa.codigo_dependencia || \"unknown\";\n                const depName = opa.dependency_name || opa.dependencia || \"Dependencia Desconocida\";\n                if (!dependencyMap.has(depCode)) {\n                    dependencyMap.set(depCode, {\n                        name: depName,\n                        sigla: this.extractSigla(depName),\n                        tramites: [],\n                        opas: [],\n                        subdependenciasCount: 0 // Se calculará después\n                    });\n                }\n                dependencyMap.get(depCode).opas.push(opa);\n            });\n        } catch (error) {\n            console.error(\"Error procesando dependencias:\", error);\n            throw error;\n        }\n        // Calcular conteos de subdependencias para cada dependencia\n        try {\n            for (const [depCode, depData] of dependencyMap.entries()){\n                // Buscar la dependencia en la base de datos por nombre\n                const { data: dependencyData } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"dependencies\").select(\"id\").ilike(\"name\", \"%\".concat(depData.name, \"%\")).eq(\"is_active\", true).single();\n                if (dependencyData) {\n                    // Contar subdependencias activas\n                    const { count: subdependenciasCount } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"subdependencies\").select(\"*\", {\n                        count: \"exact\",\n                        head: true\n                    }).eq(\"dependency_id\", dependencyData.id).eq(\"is_active\", true);\n                    depData.subdependenciasCount = subdependenciasCount || 0;\n                }\n            }\n        } catch (error) {\n            console.error(\"Error calculando subdependencias:\", error);\n        // No lanzar error, continuar con conteos en 0\n        }\n        // Convertir a formato Dependency\n        dependencyMap.forEach((depData, depCode)=>{\n            const dependency = {\n                id: depCode,\n                name: depData.name,\n                sigla: depData.sigla,\n                tramitesCount: depData.tramites.length,\n                opasCount: depData.opas.length,\n                totalProcedures: depData.tramites.length + depData.opas.length,\n                subdependenciasCount: depData.subdependenciasCount || 0,\n                description: this.generateDescription(depData.name),\n                icon: this.assignIcon(depData.name),\n                color: this.assignColor(depCode)\n            };\n            this.dependencies.set(depCode, dependency);\n        });\n    }\n    /**\n   * Crear las 14 dependencias oficiales del municipio de Chía\n   */ async createOfficialDependencies() {\n        // Definir las 14 dependencias oficiales del municipio de Chía\n        const officialDependencies = [\n            {\n                id: \"despacho-alcalde\",\n                name: \"Despacho del Alcalde\",\n                sigla: \"DA\"\n            },\n            {\n                id: \"secretaria-general\",\n                name: \"Secretar\\xeda General\",\n                sigla: \"SG\"\n            },\n            {\n                id: \"secretaria-gobierno\",\n                name: \"Secretar\\xeda de Gobierno y Convivencia\",\n                sigla: \"SGC\"\n            },\n            {\n                id: \"secretaria-hacienda\",\n                name: \"Secretar\\xeda de Hacienda\",\n                sigla: \"SH\"\n            },\n            {\n                id: \"secretaria-planeacion\",\n                name: \"Secretar\\xeda de Planeaci\\xf3n\",\n                sigla: \"SP\"\n            },\n            {\n                id: \"secretaria-desarrollo-social\",\n                name: \"Secretar\\xeda de Desarrollo Social\",\n                sigla: \"SDS\"\n            },\n            {\n                id: \"secretaria-educacion\",\n                name: \"Secretar\\xeda de Educaci\\xf3n\",\n                sigla: \"SE\"\n            },\n            {\n                id: \"secretaria-salud\",\n                name: \"Secretar\\xeda de Salud\",\n                sigla: \"SS\"\n            },\n            {\n                id: \"secretaria-movilidad\",\n                name: \"Secretar\\xeda de Movilidad\",\n                sigla: \"SM\"\n            },\n            {\n                id: \"secretaria-desarrollo-economico\",\n                name: \"Secretar\\xeda de Desarrollo Econ\\xf3mico\",\n                sigla: \"SDE\"\n            },\n            {\n                id: \"secretaria-infraestructura\",\n                name: \"Secretar\\xeda de Infraestructura\",\n                sigla: \"SI\"\n            },\n            {\n                id: \"secretaria-ambiente\",\n                name: \"Secretar\\xeda de Ambiente\",\n                sigla: \"SA\"\n            },\n            {\n                id: \"secretaria-tecnologias\",\n                name: \"Secretar\\xeda de Tecnolog\\xedas de la Informaci\\xf3n\",\n                sigla: \"STI\"\n            },\n            {\n                id: \"entidades-descentralizadas\",\n                name: \"Entidades Descentralizadas\",\n                sigla: \"ED\"\n            }\n        ];\n        // Obtener contadores reales de trámites y OPAs\n        const counters = await this.getDependencyCounters();\n        // Crear cada dependencia oficial\n        officialDependencies.forEach((depInfo, index)=>{\n            const counter = counters.get(depInfo.name) || {\n                tramites: 0,\n                opas: 0,\n                subdependencies: 0\n            };\n            const dependency = {\n                id: depInfo.id,\n                name: depInfo.name,\n                sigla: depInfo.sigla,\n                tramitesCount: counter.tramites,\n                opasCount: counter.opas,\n                totalProcedures: counter.tramites + counter.opas,\n                subdependenciasCount: counter.subdependencies,\n                description: this.generateDescription(depInfo.name),\n                icon: this.assignIcon(depInfo.name),\n                color: this.assignColor(depInfo.id)\n            };\n            this.dependencies.set(depInfo.id, dependency);\n        });\n    }\n    /**\n   * Obtener contadores de trámites y OPAs por dependencia\n   */ async getDependencyCounters() {\n        const counters = new Map();\n        try {\n            var // Procesar trámites\n            _tramitesResult_data, // Procesar OPAs\n            _opasResult_data;\n            // Obtener datos de Supabase con JOIN para obtener nombres de dependencias\n            const [tramitesResult, opasResult] = await Promise.all([\n                _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"procedures\").select(\"\\n            id,\\n            dependencies!inner(name, code)\\n          \"),\n                _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"opas\").select(\"\\n            id,\\n            dependencies!inner(name, code)\\n          \")\n            ]);\n            (_tramitesResult_data = tramitesResult.data) === null || _tramitesResult_data === void 0 ? void 0 : _tramitesResult_data.forEach((item)=>{\n                var _item_dependencies;\n                const depName = this.mapToOfficialDependency((_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.name);\n                if (!counters.has(depName)) {\n                    counters.set(depName, {\n                        tramites: 0,\n                        opas: 0,\n                        subdependencies: 0\n                    });\n                }\n                counters.get(depName).tramites++;\n            });\n            (_opasResult_data = opasResult.data) === null || _opasResult_data === void 0 ? void 0 : _opasResult_data.forEach((item)=>{\n                var _item_dependencies;\n                const depName = this.mapToOfficialDependency((_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.name);\n                if (!counters.has(depName)) {\n                    counters.set(depName, {\n                        tramites: 0,\n                        opas: 0,\n                        subdependencies: 0\n                    });\n                }\n                counters.get(depName).opas++;\n            });\n        } catch (error) {\n            console.error(\"Error obteniendo contadores:\", error);\n        }\n        return counters;\n    }\n    /**\n   * Mapear nombres de dependencias de los datos a las dependencias oficiales\n   */ mapToOfficialDependency(originalName) {\n        if (!originalName) return \"Entidades Descentralizadas\";\n        const mappings = {\n            \"Despacho Alcalde\": \"Despacho del Alcalde\",\n            \"Secretaria General\": \"Secretar\\xeda General\",\n            \"Secretaria de Gobierno y Convivencia\": \"Secretar\\xeda de Gobierno y Convivencia\",\n            \"Secretaria de Hacienda\": \"Secretar\\xeda de Hacienda\",\n            \"Secretaria de Planeacion\": \"Secretar\\xeda de Planeaci\\xf3n\",\n            \"Secretaria de Desarrollo Social\": \"Secretar\\xeda de Desarrollo Social\",\n            \"Secretaria de Educacion\": \"Secretar\\xeda de Educaci\\xf3n\",\n            \"Secretaria de Salud\": \"Secretar\\xeda de Salud\",\n            \"Secretaria de Movilidad\": \"Secretar\\xeda de Movilidad\",\n            \"Secretaria para el Desarrollo Economico\": \"Secretar\\xeda de Desarrollo Econ\\xf3mico\",\n            \"Secretaria de Infraestructura\": \"Secretar\\xeda de Infraestructura\",\n            \"Secretaria de Medio Ambiente\": \"Secretar\\xeda de Ambiente\",\n            \"Secretaria de Participacion Ciudadana y Accion Comunitaria\": \"Secretar\\xeda de Gobierno y Convivencia\",\n            \"Descentralizados\": \"Entidades Descentralizadas\"\n        };\n        return mappings[originalName] || originalName;\n    }\n    /**\n   * Extrae sigla de un nombre de dependencia\n   */ extractSigla(name) {\n        if (name.includes(\"Secretar\\xeda\")) {\n            const words = name.replace(\"Secretar\\xeda de \", \"\").split(\" \");\n            return words.map((word)=>word.charAt(0).toUpperCase()).join(\"\");\n        }\n        const words = name.split(\" \");\n        if (words.length === 1) return words[0].substring(0, 3).toUpperCase();\n        return words.map((word)=>word.charAt(0).toUpperCase()).join(\"\").substring(0, 4);\n    }\n    /**\n   * Genera descripción para una dependencia\n   */ generateDescription(name) {\n        const descriptions = {\n            \"Despacho del Alcalde\": \"Oficina principal del Alcalde Municipal y coordinaci\\xf3n general\",\n            \"Secretar\\xeda General\": \"Gesti\\xf3n administrativa, jur\\xeddica y de archivo municipal\",\n            \"Secretar\\xeda de Gobierno y Convivencia\": \"Orden p\\xfablico, convivencia ciudadana y participaci\\xf3n comunitaria\",\n            \"Secretar\\xeda de Hacienda\": \"Administraci\\xf3n financiera, tributaria y presupuestal municipal\",\n            \"Secretar\\xeda de Planeaci\\xf3n\": \"Ordenamiento territorial, desarrollo urbano y gesti\\xf3n de proyectos\",\n            \"Secretar\\xeda de Desarrollo Social\": \"Programas sociales, atenci\\xf3n a poblaci\\xf3n vulnerable y desarrollo comunitario\",\n            \"Secretar\\xeda de Educaci\\xf3n\": \"Administraci\\xf3n del sistema educativo municipal y programas pedag\\xf3gicos\",\n            \"Secretar\\xeda de Salud\": \"Vigilancia epidemiol\\xf3gica, control sanitario y salud p\\xfablica\",\n            \"Secretar\\xeda de Movilidad\": \"Gesti\\xf3n del tr\\xe1nsito, transporte p\\xfablico y movilidad sostenible\",\n            \"Secretar\\xeda de Desarrollo Econ\\xf3mico\": \"Promoci\\xf3n empresarial, turismo y desarrollo econ\\xf3mico local\",\n            \"Secretar\\xeda de Infraestructura\": \"Obras p\\xfablicas, mantenimiento vial y proyectos de infraestructura\",\n            \"Secretar\\xeda de Ambiente\": \"Gesti\\xf3n ambiental, recursos naturales y sostenibilidad\",\n            \"Secretar\\xeda de Tecnolog\\xedas de la Informaci\\xf3n\": \"Sistemas de informaci\\xf3n, gobierno digital y tecnolog\\xeda\",\n            \"Entidades Descentralizadas\": \"Empresas de servicios p\\xfablicos y entidades adscritas al municipio\"\n        };\n        return descriptions[name] || \"Gesti\\xf3n de procedimientos administrativos de \".concat(name);\n    }\n    /**\n   * Asigna icono representativo para una dependencia\n   */ assignIcon(name) {\n        const icons = {\n            \"Despacho del Alcalde\": \"crown\",\n            \"Secretar\\xeda General\": \"folder\",\n            \"Secretar\\xeda de Gobierno y Convivencia\": \"shield\",\n            \"Secretar\\xeda de Hacienda\": \"banknote\",\n            \"Secretar\\xeda de Planeaci\\xf3n\": \"map\",\n            \"Secretar\\xeda de Desarrollo Social\": \"users\",\n            \"Secretar\\xeda de Educaci\\xf3n\": \"graduation-cap\",\n            \"Secretar\\xeda de Salud\": \"heart-pulse\",\n            \"Secretar\\xeda de Movilidad\": \"car\",\n            \"Secretar\\xeda de Desarrollo Econ\\xf3mico\": \"building\",\n            \"Secretar\\xeda de Infraestructura\": \"building\",\n            \"Secretar\\xeda de Ambiente\": \"folder\",\n            \"Secretar\\xeda de Tecnolog\\xedas de la Informaci\\xf3n\": \"folder\",\n            \"Entidades Descentralizadas\": \"building\"\n        };\n        return icons[name] || \"folder\";\n    }\n    /**\n   * Asigna color temático para una dependencia\n   */ assignColor(depCode) {\n        const colors = [\n            \"bg-chia-blue-100 border-chia-blue-300 hover:bg-chia-blue-200\",\n            \"bg-chia-green-100 border-chia-green-300 hover:bg-chia-green-200\",\n            \"bg-blue-100 border-blue-300 hover:bg-blue-200\",\n            \"bg-green-100 border-green-300 hover:bg-green-200\",\n            \"bg-purple-100 border-purple-300 hover:bg-purple-200\",\n            \"bg-orange-100 border-orange-300 hover:bg-orange-200\",\n            \"bg-teal-100 border-teal-300 hover:bg-teal-200\",\n            \"bg-indigo-100 border-indigo-300 hover:bg-indigo-200\",\n            \"bg-pink-100 border-pink-300 hover:bg-pink-200\"\n        ];\n        const index = parseInt(depCode) % colors.length;\n        return colors[index];\n    }\n    /**\n   * Obtiene todas las dependencias directamente desde la base de datos\n   */ async getAllDependencies() {\n        try {\n            // Obtener dependencias con contadores desde la base de datos\n            const { data: dependenciesData, error: dependenciesError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"dependencies\").select(\"id, name, code, acronym\").order(\"name\");\n            if (dependenciesError) {\n                console.error(\"Error obteniendo dependencias:\", dependenciesError);\n                return [];\n            }\n            // Obtener contadores de trámites y OPAs para cada dependencia\n            const dependencies = [];\n            for (const dep of dependenciesData || []){\n                // Contar trámites\n                const { count: tramitesCount } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"procedures\").select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"dependency_id\", dep.id);\n                // Contar OPAs\n                const { count: opasCount } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"opas\").select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"dependency_id\", dep.id);\n                // Contar subdependencias activas\n                const { count: subdependenciasCount } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"subdependencies\").select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"dependency_id\", dep.id).eq(\"is_active\", true);\n                const dependency = {\n                    id: dep.id,\n                    name: dep.name,\n                    sigla: dep.acronym || dep.code || \"\",\n                    tramitesCount: tramitesCount || 0,\n                    opasCount: opasCount || 0,\n                    totalProcedures: (tramitesCount || 0) + (opasCount || 0),\n                    subdependenciasCount: subdependenciasCount || 0,\n                    description: this.generateDescription(dep.name),\n                    icon: this.assignIcon(dep.name),\n                    color: this.assignColor(dep.id)\n                };\n                dependencies.push(dependency);\n            }\n            return dependencies.sort((a, b)=>b.totalProcedures - a.totalProcedures);\n        } catch (error) {\n            console.error(\"Error en getAllDependencies:\", error);\n            return [];\n        }\n    }\n    /**\n   * Obtiene una dependencia por ID\n   */ async getDependencyById(id) {\n        await this.initialize();\n        return this.dependencies.get(id) || null;\n    }\n    /**\n   * Obtiene estadísticas generales de dependencias directamente desde la base de datos\n   */ async getDependencyStats() {\n        try {\n            // Contar dependencias\n            const { count: totalDependencies } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"dependencies\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            });\n            // Contar trámites\n            const { count: totalTramites } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"procedures\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            });\n            // Contar OPAs\n            const { count: totalOPAs } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"opas\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            });\n            const totalProcedures = (totalTramites || 0) + (totalOPAs || 0);\n            const averageProceduresPerDependency = totalDependencies > 0 ? Math.round(totalProcedures / totalDependencies) : 0;\n            return {\n                totalDependencies: totalDependencies || 0,\n                totalTramites: totalTramites || 0,\n                totalOPAs: totalOPAs || 0,\n                totalProcedures,\n                averageProceduresPerDependency\n            };\n        } catch (error) {\n            console.error(\"Error en getDependencyStats:\", error);\n            return {\n                totalDependencies: 0,\n                totalTramites: 0,\n                totalOPAs: 0,\n                totalProcedures: 0,\n                averageProceduresPerDependency: 0\n            };\n        }\n    }\n    /**\n   * Busca dependencias por nombre\n   */ async searchDependencies(query) {\n        await this.initialize();\n        const searchTerm = query.toLowerCase().trim();\n        if (!searchTerm) return this.getAllDependencies();\n        return Array.from(this.dependencies.values()).filter((dep)=>dep.name.toLowerCase().includes(searchTerm) || dep.sigla.toLowerCase().includes(searchTerm) || dep.description && dep.description.toLowerCase().includes(searchTerm)).sort((a, b)=>b.totalProcedures - a.totalProcedures);\n    }\n    /**\n   * Obtiene procedimientos de una dependencia específica\n   */ async getProceduresByDependency(dependencyId) {\n        await this.initialize();\n        const dependency = this.dependencies.get(dependencyId);\n        if (!dependency) return null;\n        try {\n            // Primero obtener el UUID de la dependencia desde la base de datos\n            const { data: depData, error: depError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"dependencies\").select(\"id, name\").ilike(\"name\", \"%\".concat(dependency.name.replace(\"Secretar\\xeda\", \"Secretaria\"), \"%\")).single();\n            if (depError || !depData) {\n                console.error(\"Error obteniendo dependencia:\", depError);\n                return {\n                    dependency,\n                    tramites: [],\n                    opas: []\n                };\n            }\n            // Obtener trámites de esta dependencia desde Supabase\n            const { data: tramites, error: tramitesError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"procedures\").select(\"\\n          *,\\n          dependencies!inner(name, code)\\n        \").eq(\"dependency_id\", depData.id);\n            if (tramitesError) {\n                console.error(\"Error obteniendo tr\\xe1mites:\", tramitesError);\n            }\n            // Obtener OPAs de esta dependencia desde Supabase\n            const { data: opas, error: opasError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"opas\").select(\"\\n          *,\\n          dependencies!inner(name, code)\\n        \").eq(\"dependency_id\", depData.id);\n            if (opasError) {\n                console.error(\"Error obteniendo OPAs:\", opasError);\n            }\n            return {\n                dependency,\n                tramites: tramites || [],\n                opas: opas || []\n            };\n        } catch (error) {\n            console.error(\"Error obteniendo procedimientos por dependencia:\", error);\n            return {\n                dependency,\n                tramites: [],\n                opas: []\n            };\n        }\n    }\n    /**\n   * Obtiene las dependencias más populares (con más procedimientos)\n   */ async getTopDependencies() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 6;\n        await this.initialize();\n        return Array.from(this.dependencies.values()).sort((a, b)=>b.totalProcedures - a.totalProcedures).slice(0, limit);\n    }\n    constructor(){\n        this.dependencies = new Map();\n        this.initialized = false;\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (DependencyService.getInstance());\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/dependencyService.ts\n"));

/***/ })

});