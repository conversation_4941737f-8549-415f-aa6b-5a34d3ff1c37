import { supabase } from '@/lib/supabase/client'
import dependencyServiceInstance, { type Dependency } from '@/lib/services/dependencyService'

// Tipos para los resultados de búsqueda
export interface SearchResult {
  id: string
  name: string
  type: 'TRAMITE' | 'OPA' | 'DEPENDENCIA'
  description?: string
  dependency: string
  subdependency?: string
  cost?: string
  responseTime?: string
  popularity?: number
  matchScore?: number
  highlightedName?: string
  suitUrl?: string
  govUrl?: string
  formRequired?: boolean
  // Campos específicos para dependencias
  sigla?: string
  tramitesCount?: number
  opasCount?: number
  totalProcedures?: number
  subdependenciasCount?: number
  icon?: string
  color?: string
}

export interface SearchFilters {
  dependency?: string
  type?: 'TRAMITE' | 'OPA' | 'DEPENDENCIA' | 'ALL'
  hasCost?: boolean
  maxResponseTime?: number
}

export interface SearchOptions {
  limit?: number
  offset?: number
  includeHighlight?: boolean
  sortBy?: 'relevance' | 'name' | 'popularity' | 'responseTime'
  sortOrder?: 'asc' | 'desc'
}

/**
 * Servicio de búsqueda inteligente para trámites y OPAs
 */
export class SearchService {
  private supabaseClient = supabase
  private tramites: any[] = []
  private opas: any[] = []
  private dependencies: Dependency[] = []
  private dependencyService = dependencyServiceInstance
  private initialized = false

  constructor() {
    this.initializeData()
  }

  /**
   * Inicializar datos de trámites y OPAs
   */
  private async initializeData() {
    if (this.initialized) return

    try {
      // Cargar trámites desde Supabase
      const { data: tramitesData, error: tramitesError } = await supabase
        .from('procedures')
        .select('*')

      if (tramitesError) {
        console.error('Error cargando trámites:', tramitesError)
        throw tramitesError
      }

      this.tramites = (tramitesData || []).map((tramite, index) => ({
        id: `tramite-${tramite.id || index}`,
        name: tramite.name || tramite.Nombre || 'Sin nombre',
        type: 'TRAMITE' as const,
        description: tramite.description || `Trámite de ${tramite.name || tramite.Nombre}`,
        dependency: tramite.dependency_name || tramite.dependencia || 'Sin dependencia',
        subdependency: tramite.subdependency_name || tramite.subdependencia,
        cost: tramite.cost || tramite['¿Tiene pago?'] || 'No especificado',
        responseTime: tramite.response_time || tramite['Tiempo de respuesta'] || 'No especificado',
        suitUrl: tramite.suit_url || tramite['Visualización trámite en el SUIT'],
        govUrl: tramite.gov_url || tramite['Visualización trámite en GOV.CO'],
        formRequired: tramite.form_required || tramite.Formulario === 'Sí',
        popularity: tramite.popularity || Math.floor(Math.random() * 100) + 1
      }))

      // Cargar OPAs desde Supabase
      const { data: opasData, error: opasError } = await supabase
        .from('opas')
        .select('*')

      if (opasError) {
        console.error('Error cargando OPAs:', opasError)
        throw opasError
      }

      this.opas = (opasData || []).map((opa, index) => ({
        id: `opa-${opa.id || index}`,
        name: opa.name || opa.OPA || 'Sin nombre',
        type: 'OPA' as const,
        description: opa.description || `OPA de ${opa.name || opa.OPA}`,
        dependency: opa.dependency_name || opa.dependencia || 'Sin dependencia',
        subdependency: opa.subdependency_name || opa.subdependencia,
        cost: opa.cost || opa.Costo || 'No especificado',
        responseTime: opa.response_time || opa.Tiempo_respuesta || 'No especificado',
        suitUrl: opa.suit_url || opa.URL_SUIT,
        govUrl: opa.gov_url || opa.URL_GOV,
        popularity: opa.popularity || Math.floor(Math.random() * 100) + 1
      }))

      // Cargar dependencias
      await this.dependencyService.initialize()
      this.dependencies = await this.dependencyService.getAllDependencies()

      this.initialized = true
    } catch (error) {
      console.error('Error inicializando datos de búsqueda:', error)
    }
  }



  /**
   * Realizar búsqueda inteligente
   */
  async search(
    query: string,
    filters: SearchFilters = {},
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    await this.initializeData()

    const {
      limit = 10,
      offset = 0,
      includeHighlight = true,
      sortBy = 'relevance',
      sortOrder = 'desc'
    } = options

    if (!query.trim()) {
      return []
    }

    const searchTerm = query.toLowerCase().trim()
    let allProcedures = [...this.tramites, ...this.opas]
    let dependencyResults: SearchResult[] = []

    // Buscar en dependencias si no hay filtro específico o si incluye dependencias
    if (!filters.type || filters.type === 'ALL' || filters.type === 'DEPENDENCIA') {
      dependencyResults = this.dependencies
        .map(dep => {
          const matchScore = this.calculateDependencyMatchScore(dep, searchTerm)
          if (matchScore === 0) return null

          return {
            id: dep.id,
            name: dep.name,
            type: 'DEPENDENCIA' as const,
            description: dep.description || `Dependencia municipal: ${dep.name}`,
            dependency: dep.name,
            sigla: dep.sigla,
            tramitesCount: dep.tramitesCount,
            opasCount: dep.opasCount,
            totalProcedures: dep.totalProcedures,
            subdependenciasCount: dep.subdependenciasCount,
            icon: dep.icon,
            color: dep.color,
            matchScore,
            highlightedName: includeHighlight ? this.highlightMatch(dep.name, searchTerm) : dep.name
          }
        })
        .filter(Boolean) as SearchResult[]
    }

    // Aplicar filtros a procedimientos
    if (filters.type && filters.type !== 'ALL' && filters.type !== 'DEPENDENCIA') {
      allProcedures = allProcedures.filter(proc => proc.type === filters.type)
    }

    if (filters.dependency) {
      allProcedures = allProcedures.filter(proc =>
        proc.dependency.toLowerCase().includes(filters.dependency!.toLowerCase())
      )
    }

    if (filters.hasCost !== undefined) {
      allProcedures = allProcedures.filter(proc => {
        const isFree = proc.cost === 'Gratuito' || proc.cost === 'No'
        return filters.hasCost ? !isFree : isFree
      })
    }

    // Realizar búsqueda con scoring en procedimientos
    const procedureResults = allProcedures
      .map(proc => {
        const matchScore = this.calculateMatchScore(proc, searchTerm)
        if (matchScore === 0) return null

        return {
          ...proc,
          matchScore,
          highlightedName: includeHighlight ? this.highlightMatch(proc.name, searchTerm) : proc.name
        }
      })
      .filter(Boolean) as SearchResult[]

    // Combinar todos los resultados
    const results = [...dependencyResults, ...procedureResults]

    // Ordenar resultados
    results.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return sortOrder === 'asc'
            ? a.name.localeCompare(b.name)
            : b.name.localeCompare(a.name)
        case 'popularity':
          return sortOrder === 'asc'
            ? (a.popularity || 0) - (b.popularity || 0)
            : (b.popularity || 0) - (a.popularity || 0)
        case 'relevance':
        default:
          return (b.matchScore || 0) - (a.matchScore || 0)
      }
    })

    return results.slice(offset, offset + limit)
  }

  /**
   * Calcular score de relevancia para un procedimiento
   */
  private calculateMatchScore(procedure: any, searchTerm: string): number {
    let score = 0
    const name = procedure.name.toLowerCase()
    const description = (procedure.description || '').toLowerCase()
    const dependency = procedure.dependency.toLowerCase()

    // Coincidencia exacta en el nombre (peso alto)
    if (name === searchTerm) {
      score += 100
    }
    // Coincidencia al inicio del nombre
    else if (name.startsWith(searchTerm)) {
      score += 80
    }
    // Coincidencia en el nombre
    else if (name.includes(searchTerm)) {
      score += 60
    }

    // Coincidencia en la descripción
    if (description.includes(searchTerm)) {
      score += 30
    }

    // Coincidencia en la dependencia
    if (dependency.includes(searchTerm)) {
      score += 20
    }

    // Bonus por popularidad
    if (procedure.popularity) {
      score += Math.floor(procedure.popularity / 10)
    }

    // Bonus por palabras múltiples
    const searchWords = searchTerm.split(' ').filter(word => word.length > 2)
    if (searchWords.length > 1) {
      const wordMatches = searchWords.filter(word =>
        name.includes(word) || description.includes(word)
      ).length
      score += (wordMatches / searchWords.length) * 20
    }

    return score
  }

  /**
   * Calcular score de relevancia para una dependencia
   */
  private calculateDependencyMatchScore(dependency: Dependency, searchTerm: string): number {
    let score = 0
    const name = dependency.name.toLowerCase()
    const sigla = (dependency.sigla || '').toLowerCase()
    const description = (dependency.description || '').toLowerCase()

    // Coincidencia exacta en el nombre (peso alto)
    if (name === searchTerm) {
      score += 100
    }
    // Coincidencia exacta en la sigla
    else if (sigla === searchTerm) {
      score += 95
    }
    // Coincidencia al inicio del nombre
    else if (name.startsWith(searchTerm)) {
      score += 80
    }
    // Coincidencia en el nombre
    else if (name.includes(searchTerm)) {
      score += 60
    }
    // Coincidencia en la sigla
    else if (sigla.includes(searchTerm)) {
      score += 50
    }

    // Coincidencia en la descripción
    if (description.includes(searchTerm)) {
      score += 30
    }

    // Bonus por cantidad de procedimientos (dependencias más activas)
    if (dependency.totalProcedures) {
      score += Math.floor(dependency.totalProcedures / 10)
    }

    // Bonus por palabras múltiples
    const searchWords = searchTerm.split(' ').filter(word => word.length > 2)
    if (searchWords.length > 1) {
      const wordMatches = searchWords.filter(word =>
        name.includes(word) || description.includes(word) || sigla.includes(word)
      ).length
      score += (wordMatches / searchWords.length) * 20
    }

    return score
  }

  /**
   * Resaltar coincidencias en el texto
   */
  private highlightMatch(text: string, searchTerm: string): string {
    if (!searchTerm.trim()) return text

    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
    return text.replace(regex, '<mark>$1</mark>')
  }

  /**
   * Obtener sugerencias populares
   */
  async getPopularSearches(limit: number = 10): Promise<string[]> {
    await this.initializeData()

    // En producción, esto vendría de analytics/métricas reales
    const popularTerms = [
      'licencia construcción',
      'certificado residencia',
      'impuesto predial',
      'licencia comercial',
      'certificado libertad',
      'permiso eventos',
      'paz y salvo',
      'certificado estratificación',
      'licencia funcionamiento',
      'registro mercantil'
    ]

    return popularTerms.slice(0, limit)
  }

  /**
   * Obtener dependencias disponibles para filtros
   */
  async getDependencies(): Promise<string[]> {
    await this.initializeData()

    const dependencies = new Set<string>()

    this.tramites.forEach(tramite => {
      if (tramite.dependency) {
        dependencies.add(tramite.dependency)
      }
    })

    this.opas.forEach(opa => {
      if (opa.dependency) {
        dependencies.add(opa.dependency)
      }
    })

    return Array.from(dependencies).sort()
  }

  /**
   * Obtener estadísticas de búsqueda
   */
  async getSearchStats(): Promise<{
    totalTramites: number
    totalOPAs: number
    totalDependencies: number
  }> {
    await this.initializeData()

    const dependencies = await this.getDependencies()

    return {
      totalTramites: this.tramites.length,
      totalOPAs: this.opas.length,
      totalDependencies: dependencies.length
    }
  }

  /**
   * Obtener lista de dependencias disponibles para filtros
   */
  async getAvailableDependencies(): Promise<string[]> {
    await this.initializeData()

    const dependencyNames = new Set<string>()

    // Agregar dependencias de trámites
    this.tramites.forEach(tramite => {
      if (tramite.dependency) {
        dependencyNames.add(tramite.dependency)
      }
    })

    // Agregar dependencias de OPAs
    this.opas.forEach(opa => {
      if (opa.dependency) {
        dependencyNames.add(opa.dependency)
      }
    })

    // Agregar dependencias del servicio de dependencias
    this.dependencies.forEach(dep => {
      dependencyNames.add(dep.name)
    })

    return Array.from(dependencyNames).sort()
  }
}

// Instancia singleton del servicio de búsqueda
export const searchService = new SearchService()