{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958b10da1a3fd9d5-MIA", "connection": "close", "content-encoding": "br", "content-location": "/subdependencies?dependency_id=eq.b153970f-168b-4f41-8069-1cfbcf9c4929&is_active=eq.true&select=%2A", "content-profile": "public", "content-range": "0-13/14", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:30:40 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=Hu8CZqkvmxQuAd1iJtCrWsl0A3d_EZowDtXO95MM5yw-1751427040-1.0.1.1-mHPq6Jd4yWZVb6U_U6TBqxygRXGLcgSdzUPQiwxsGs78fDnJa_xZ2OJc3vVfgMKGBLvFMCUPtUuFd8owP7klxpq7NflR0ouPkY1DJTJfn28; path=/; expires=Wed, 02-Jul-25 04:00:40 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/subdependencies?select=*&dependency_id=eq.b153970f-168b-4f41-8069-1cfbcf9c4929&is_active=eq.true"}, "revalidate": 31536000, "tags": []}