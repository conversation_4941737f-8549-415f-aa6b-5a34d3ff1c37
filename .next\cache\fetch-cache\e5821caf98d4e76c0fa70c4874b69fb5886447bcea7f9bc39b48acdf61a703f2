{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef88ac29498c-MIA", "connection": "close", "content-encoding": "br", "content-location": "/procedures?dependency_id=eq.d3aaa47f-9f94-4574-9ba1-79052e891ba6&select=%2A", "content-profile": "public", "content-range": "0-30/31", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:55 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=3JCn0HLcisq6wjuQ9ZMpi_nxoXb6Ogy_C2fqejQXBsE-1751425675-1.0.1.1-KqcPLaj0bjSCMtCpCxfsJjasUqm1OVUK_z_BOCyUMLSQjsJ20WHkJGRjLm.a4tmJmPlaQD1T5g6S5qYtTqRyzitl_muS5zhmJ2m_KgV2lbo; path=/; expires=Wed, 02-Jul-25 03:37:55 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/procedures?select=*&dependency_id=eq.d3aaa47f-9f94-4574-9ba1-79052e891ba6"}, "revalidate": 31536000, "tags": []}