{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef606e5c4960-MIA", "connection": "close", "content-encoding": "br", "content-location": "/procedures?dependency_id=eq.69ce96bb-faee-403f-92a6-e489e1536d02&select=%2A", "content-profile": "public", "content-range": "0-3/4", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:49 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=_.SZPKyeh1pjw1lhFkJeRWFYqegKNE1wlcNL.0gT9ZE-1751425669-1.0.1.1-n7R_8znUv.qq3au3WLo_eLJEXTA10_NNvFGC3cd0QAdi8tJocOLle_INYSjyW5vytI3iV_0iQnlC_K73PLkdJnXqAjG.tgwkdhXFF2CnI6w; path=/; expires=Wed, 02-Jul-25 03:37:49 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/procedures?select=*&dependency_id=eq.69ce96bb-faee-403f-92a6-e489e1536d02"}, "revalidate": 31536000, "tags": []}