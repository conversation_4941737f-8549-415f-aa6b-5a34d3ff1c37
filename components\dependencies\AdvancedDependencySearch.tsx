'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Search, X, Filter, Building2, FileText, BarChart3, Loader2, ChevronDown, AlertCircle } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { useDebounce } from '@/hooks/useDebounce'
import { searchService, SearchResult } from '@/lib/services/searchService'
import { SearchLoading, SearchResultsLoading } from '@/components/ui/loading-states'
import { SearchEmptyState, InlineError } from '@/components/ui/error-states'

interface SearchFilters {
  type: 'ALL' | 'DEPENDENCIA' | 'SUBDEPENDENCIA' | 'TRAMITE' | 'OPA'
  showFilters: boolean
  selectedDependency?: string
}

interface SearchError {
  type: 'network' | 'timeout' | 'server' | 'general'
  message: string
  retryable: boolean
}

interface AdvancedDependencySearchProps {
  onSearch: (query: string, results: SearchResult[]) => void
  onResultSelect: (result: SearchResult) => void
  placeholder?: string
  className?: string
  maxResults?: number
  showFilters?: boolean
  autoFocus?: boolean
  onError?: (error: SearchError) => void
  enableRetry?: boolean
}

export function AdvancedDependencySearch({
  onSearch,
  onResultSelect,
  placeholder = "Buscar dependencias, trámites o servicios...",
  className,
  maxResults = 10,
  showFilters = true,
  autoFocus = false,
  onError,
  enableRetry = true
}: AdvancedDependencySearchProps) {
  // Estados del componente
  const [query, setQuery] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [results, setResults] = useState<SearchResult[]>([])
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const [error, setError] = useState<SearchError | null>(null)
  const [retryCount, setRetryCount] = useState(0)
  const [filters, setFilters] = useState<SearchFilters>({
    type: 'ALL',
    showFilters: false
  })
  const [availableDependencies, setAvailableDependencies] = useState<string[]>([])

  // Referencias
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Debounced query para optimizar las búsquedas
  const debouncedQuery = useDebounce(query, 300)

  // Efecto para realizar búsquedas
  useEffect(() => {
    if (debouncedQuery.trim()) {
      performSearch(debouncedQuery)
    } else {
      setResults([])
      setIsOpen(false)
    }
  }, [debouncedQuery, filters.type])

  // Cerrar dropdown al hacer clic fuera
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSelectedIndex(-1)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Función para realizar búsqueda
  const performSearch = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim()) return

    setIsLoading(true)
    setError(null)

    try {
      const searchResults = await searchService.search(
        searchQuery,
        { type: filters.type },
        { limit: maxResults, includeHighlight: true }
      )

      setResults(searchResults)
      setIsOpen(true)
      setSelectedIndex(-1)
      setRetryCount(0)
      onSearch(searchQuery, searchResults)
    } catch (err) {
      console.error('Error en búsqueda:', err)

      const searchError: SearchError = {
        type: 'general',
        message: 'Error al realizar la búsqueda',
        retryable: true
      }

      // Determinar tipo de error específico
      if (err instanceof Error) {
        if (err.message.includes('network') || err.message.includes('fetch')) {
          searchError.type = 'network'
          searchError.message = 'Error de conexión. Verifica tu internet.'
        } else if (err.message.includes('timeout')) {
          searchError.type = 'timeout'
          searchError.message = 'La búsqueda tardó demasiado tiempo.'
        } else if (err.message.includes('server')) {
          searchError.type = 'server'
          searchError.message = 'Error del servidor. Intenta más tarde.'
        }
      }

      setError(searchError)
      setResults([])
      setIsOpen(false)

      if (onError) {
        onError(searchError)
      }
    } finally {
      setIsLoading(false)
    }
  }, [filters.type, maxResults, onSearch, onError])

  // Función para reintentar búsqueda
  const handleRetry = useCallback(() => {
    if (query.trim() && retryCount < 3) {
      setRetryCount(prev => prev + 1)
      performSearch(query)
    }
  }, [query, retryCount, performSearch])

  // Manejar cambios en el input
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setQuery(value)
    
    if (!value.trim()) {
      setResults([])
      setIsOpen(false)
    }
  }

  // Manejar teclas de navegación
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || results.length === 0) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev < results.length - 1 ? prev + 1 : prev
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1)
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0 && selectedIndex < results.length) {
          handleResultSelect(results[selectedIndex])
        }
        break
      case 'Escape':
        setIsOpen(false)
        setSelectedIndex(-1)
        inputRef.current?.blur()
        break
    }
  }

  // Manejar selección de resultado
  const handleResultSelect = (result: SearchResult) => {
    setQuery(result.name)
    setIsOpen(false)
    setSelectedIndex(-1)
    onResultSelect(result)
  }

  // Limpiar búsqueda
  const clearSearch = () => {
    setQuery('')
    setResults([])
    setIsOpen(false)
    setSelectedIndex(-1)
    inputRef.current?.focus()
  }

  // Alternar filtros
  const toggleFilters = () => {
    setFilters(prev => ({ ...prev, showFilters: !prev.showFilters }))
  }

  // Cambiar tipo de filtro
  const handleFilterChange = (type: SearchFilters['type']) => {
    setFilters(prev => ({ ...prev, type }))
  }

  // Obtener icono según el tipo
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'DEPENDENCIA':
        return <Building2 className="h-4 w-4" />
      case 'SUBDEPENDENCIA':
        return <Building className="h-4 w-4" />
      case 'TRAMITE':
        return <FileText className="h-4 w-4" />
      case 'OPA':
        return <BarChart3 className="h-4 w-4" />
      default:
        return <Search className="h-4 w-4" />
    }
  }

  // Obtener color según el tipo
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'DEPENDENCIA':
        return 'bg-chia-blue-100 text-chia-blue-700 border-chia-blue-200'
      case 'SUBDEPENDENCIA':
        return 'bg-orange-100 text-orange-700 border-orange-200'
      case 'TRAMITE':
        return 'bg-chia-green-100 text-chia-green-700 border-chia-green-200'
      case 'OPA':
        return 'bg-purple-100 text-purple-700 border-purple-200'
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  return (
    <div ref={searchRef} className={cn("relative w-full", className)}>
      {/* Barra de búsqueda principal */}
      <div className="relative">
        <div className="relative flex items-center">
          <Search className="absolute left-3 h-5 w-5 text-gray-400" />
          <Input
            ref={inputRef}
            type="text"
            placeholder={placeholder}
            value={query}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={() => {
              if (results.length > 0) setIsOpen(true)
            }}
            autoFocus={autoFocus}
            aria-label="Buscar dependencias, trámites y servicios"
            aria-expanded={isOpen}
            aria-haspopup="listbox"
            aria-autocomplete="list"
            role="combobox"
            className="pl-10 pr-20 h-12 text-base border-2 border-gray-200 focus:border-chia-blue-500 rounded-lg"
          />
          
          {/* Botones de acción */}
          <div className="absolute right-2 flex items-center space-x-1">
            {query && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearSearch}
                className="h-8 w-8 p-0 hover:bg-gray-100"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleFilters}
              className={cn(
                "h-8 w-8 p-0 hover:bg-gray-100",
                filters.showFilters && "bg-chia-blue-100 text-chia-blue-600"
              )}
            >
              <Filter className="h-4 w-4" />
            </Button>
            
            {isLoading && (
              <div className="h-8 w-8 flex items-center justify-center">
                <Loader2 className="h-4 w-4 animate-spin text-chia-blue-600" />
              </div>
            )}
          </div>
        </div>

        {/* Filtros expandibles */}
        {filters.showFilters && (
          <Card className="absolute top-full left-0 right-0 mt-2 z-50 border-2 border-gray-200">
            <CardContent className="p-4">
              <div className="flex flex-wrap gap-2">
                <span className="text-sm font-medium text-gray-700 mr-2">Filtrar por:</span>
                {(['ALL', 'DEPENDENCIA', 'SUBDEPENDENCIA', 'TRAMITE', 'OPA'] as const).map((type) => (
                  <Button
                    key={type}
                    variant={filters.type === type ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleFilterChange(type)}
                    className={cn(
                      "text-xs",
                      filters.type === type
                        ? "bg-chia-blue-600 hover:bg-chia-blue-700"
                        : "hover:bg-gray-50"
                    )}
                  >
                    {getTypeIcon(type)}
                    <span className="ml-1">
                      {type === 'ALL' ? 'Todos' :
                       type === 'DEPENDENCIA' ? 'Dependencias' :
                       type === 'SUBDEPENDENCIA' ? 'Subdependencias' :
                       type === 'TRAMITE' ? 'Trámites' : 'OPAs'}
                    </span>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Estado de carga */}
      {isOpen && isLoading && (
        <Card className="absolute top-full left-0 right-0 mt-2 z-40 border-2 border-gray-200">
          <CardContent className="p-0">
            <SearchResultsLoading />
          </CardContent>
        </Card>
      )}

      {/* Estado de error */}
      {isOpen && error && !isLoading && (
        <Card className="absolute top-full left-0 right-0 mt-2 z-40 border-2 border-red-200">
          <CardContent className="p-4">
            <InlineError
              message={error.message}
              onRetry={enableRetry && error.retryable ? handleRetry : undefined}
            />
          </CardContent>
        </Card>
      )}

      {/* Resultados de búsqueda */}
      {isOpen && !isLoading && !error && results.length > 0 && (
        <Card className="absolute top-full left-0 right-0 mt-2 z-40 border-2 border-gray-200 max-h-96 overflow-y-auto">
          <CardContent className="p-0">
            <div role="listbox" aria-label="Resultados de búsqueda">
              {results.map((result, index) => (
                <div
                  key={result.id}
                  onClick={() => handleResultSelect(result)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault()
                      handleResultSelect(result)
                    }
                  }}
                  tabIndex={0}
                  role="option"
                  aria-selected={selectedIndex === index}
                  className={cn(
                    "p-4 cursor-pointer border-b border-gray-100 last:border-b-0 hover:bg-gray-50 transition-colors",
                    "focus:outline-none focus:ring-2 focus:ring-chia-blue-500 focus:ring-inset",
                    selectedIndex === index && "bg-chia-blue-50"
                  )}
                >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      {getTypeIcon(result.type)}
                      <Badge
                        variant="outline"
                        className={cn("text-xs", getTypeColor(result.type))}
                      >
                        {result.type === 'DEPENDENCIA' ? 'Dependencia' :
                         result.type === 'TRAMITE' ? 'Trámite' : 'OPA'}
                      </Badge>
                    </div>

                    <h4
                      className="font-medium text-gray-900 truncate"
                      dangerouslySetInnerHTML={{
                        __html: result.highlightedName || result.name
                      }}
                    />

                    {result.description && (
                      <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                        {result.description}
                      </p>
                    )}

                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                      <span>{result.dependency}</span>
                      {result.type === 'DEPENDENCIA' && result.totalProcedures && (
                        <span>{result.totalProcedures} procedimientos</span>
                      )}
                      {result.cost && result.type !== 'DEPENDENCIA' && (
                        <span>{result.cost}</span>
                      )}
                    </div>
                  </div>
                </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Estado sin resultados */}
      {isOpen && !isLoading && !error && query.trim() && results.length === 0 && (
        <Card className="absolute top-full left-0 right-0 mt-2 z-40 border-2 border-gray-200">
          <CardContent className="p-0">
            <SearchEmptyState
              query={query}
              onClearSearch={() => {
                setQuery('')
                setIsOpen(false)
              }}
              onGoHome={() => {
                setQuery('')
                setIsOpen(false)
                setFilters(prev => ({ ...prev, type: 'ALL' }))
              }}
            />
          </CardContent>
        </Card>
      )}
    </div>
  )
}
