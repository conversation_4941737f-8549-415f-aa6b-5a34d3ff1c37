{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef6c3a6f6c18-BOG", "connection": "close", "content-encoding": "br", "content-location": "/opas?dependency_id=eq.c7c5b677-0a1f-4b82-be62-b3feb55285ae&select=%2A", "content-profile": "public", "content-range": "0-68/69", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:51 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=98WIQTwYkXuNiMrH1afkqIMsRrQ.ZXjK2cyGgKo3bDI-1751425671-1.0.1.1-2yJSemOJje7n7sbQ_s041iyErWun0.SLlzc1FfPHFt5j_1Wn5iaEeoPjD1QgAd7DtQEx3T6Zp_9W3l5dl0M_lEK12xy8ZI5F94XGD3qNMTU; path=/; expires=Wed, 02-Jul-25 03:37:51 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "4"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/opas?select=*&dependency_id=eq.c7c5b677-0a1f-4b82-be62-b3feb55285ae"}, "revalidate": 31536000, "tags": []}