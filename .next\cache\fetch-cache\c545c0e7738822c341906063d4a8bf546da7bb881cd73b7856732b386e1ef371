{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef793a746c18-BOG", "connection": "close", "content-encoding": "br", "content-location": "/opas?dependency_id=eq.7d2dc1c1-66c2-4da9-a0f9-5ffc34550bbf&select=%2A", "content-profile": "public", "content-range": "0-45/46", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:53 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=RSnBaO9PmH3MpxJjxsS1_6OLl2Zm.yfQeaYNh00VX3A-1751425673-1.0.1.1-ZIUalJMu9_nqE2KQPsrgYo8UebUltCQ3VINRJF8YdDN78N33UuImSAeHhSSUxmixm1XLAPavScUzF7m8fxJusw.kBgwzzEvMxF9HsAnszFk; path=/; expires=Wed, 02-Jul-25 03:37:53 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/opas?select=*&dependency_id=eq.7d2dc1c1-66c2-4da9-a0f9-5ffc34550bbf"}, "revalidate": 31536000, "tags": []}