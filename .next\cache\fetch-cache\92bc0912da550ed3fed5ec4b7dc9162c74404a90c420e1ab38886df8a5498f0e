{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef7e0cb9d9fd-MIA", "connection": "close", "content-encoding": "br", "content-location": "/opas?dependency_id=eq.be4eb6dd-b058-4f3c-a221-c88257335c0b&select=%2A", "content-profile": "public", "content-range": "0-37/38", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:53 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=MngKEkjUsVhAN6l.94G7BPSod6I7RTB4l6Ey73UnYEo-1751425673-1.0.1.1-JRnbnB9EkREciwakaiGjLlxQE33FClKSM6EEFFYSpAc.5AKDRPHC9DeLxcfY_HdEVoXua2xn9fowyAHLzz21A16woVHOZTRxsmpFaW0yL6Y; path=/; expires=Wed, 02-Jul-25 03:37:53 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/opas?select=*&dependency_id=eq.be4eb6dd-b058-4f3c-a221-c88257335c0b"}, "revalidate": 31536000, "tags": []}