{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef8b2fc04c2b-MIA", "connection": "close", "content-encoding": "br", "content-location": "/opas?dependency_id=eq.d3aaa47f-9f94-4574-9ba1-79052e891ba6&select=%2A", "content-profile": "public", "content-range": "*/0", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:56 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=O<PERSON>z<PERSON>18y4mpsRMpbjvHIefcN_tGmCDRjw.sUQnDlM-1751425676-1.0.1.1-5V1VkfJV6B5_rQI8ItwmsiCrOWHB1wHRrkIbS3rC2M1xchMARboOlxPZkfA5FPQIwT50cbPYwo0jX38ZKB1J2Jh0BUl_1R0OySGj6XDyeTQ; path=/; expires=Wed, 02-Jul-25 03:37:56 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "1"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/opas?select=*&dependency_id=eq.d3aaa47f-9f94-4574-9ba1-79052e891ba6"}, "revalidate": 31536000, "tags": []}