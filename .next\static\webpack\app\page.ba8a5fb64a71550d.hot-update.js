"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_search_IntelligentSearchBar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/search/IntelligentSearchBar */ \"(app-pages-browser)/./components/search/IntelligentSearchBar.tsx\");\n/* harmony import */ var _components_faq_FAQSection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/faq/FAQSection */ \"(app-pages-browser)/./components/faq/FAQSection.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,Building2,Calendar,CheckCircle,Clipboard,Clock,Edit3,ExternalLink,FolderOpen,Headphones,Mail,MapPin,MessageCircle,Phone,Search,Shield,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,Building2,Calendar,CheckCircle,Clipboard,Clock,Edit3,ExternalLink,FolderOpen,Headphones,Mail,MapPin,MessageCircle,Phone,Search,Shield,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,Building2,Calendar,CheckCircle,Clipboard,Clock,Edit3,ExternalLink,FolderOpen,Headphones,Mail,MapPin,MessageCircle,Phone,Search,Shield,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,Building2,Calendar,CheckCircle,Clipboard,Clock,Edit3,ExternalLink,FolderOpen,Headphones,Mail,MapPin,MessageCircle,Phone,Search,Shield,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,Building2,Calendar,CheckCircle,Clipboard,Clock,Edit3,ExternalLink,FolderOpen,Headphones,Mail,MapPin,MessageCircle,Phone,Search,Shield,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/headphones.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,Building2,Calendar,CheckCircle,Clipboard,Clock,Edit3,ExternalLink,FolderOpen,Headphones,Mail,MapPin,MessageCircle,Phone,Search,Shield,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,Building2,Calendar,CheckCircle,Clipboard,Clock,Edit3,ExternalLink,FolderOpen,Headphones,Mail,MapPin,MessageCircle,Phone,Search,Shield,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,Building2,Calendar,CheckCircle,Clipboard,Clock,Edit3,ExternalLink,FolderOpen,Headphones,Mail,MapPin,MessageCircle,Phone,Search,Shield,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,Building2,Calendar,CheckCircle,Clipboard,Clock,Edit3,ExternalLink,FolderOpen,Headphones,Mail,MapPin,MessageCircle,Phone,Search,Shield,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,Building2,Calendar,CheckCircle,Clipboard,Clock,Edit3,ExternalLink,FolderOpen,Headphones,Mail,MapPin,MessageCircle,Phone,Search,Shield,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,Building2,Calendar,CheckCircle,Clipboard,Clock,Edit3,ExternalLink,FolderOpen,Headphones,Mail,MapPin,MessageCircle,Phone,Search,Shield,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,Building2,Calendar,CheckCircle,Clipboard,Clock,Edit3,ExternalLink,FolderOpen,Headphones,Mail,MapPin,MessageCircle,Phone,Search,Shield,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,Building2,Calendar,CheckCircle,Clipboard,Clock,Edit3,ExternalLink,FolderOpen,Headphones,Mail,MapPin,MessageCircle,Phone,Search,Shield,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,Building2,Calendar,CheckCircle,Clipboard,Clock,Edit3,ExternalLink,FolderOpen,Headphones,Mail,MapPin,MessageCircle,Phone,Search,Shield,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,Building2,Calendar,CheckCircle,Clipboard,Clock,Edit3,ExternalLink,FolderOpen,Headphones,Mail,MapPin,MessageCircle,Phone,Search,Shield,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,Building2,Calendar,CheckCircle,Clipboard,Clock,Edit3,ExternalLink,FolderOpen,Headphones,Mail,MapPin,MessageCircle,Phone,Search,Shield,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,Building2,Calendar,CheckCircle,Clipboard,Clock,Edit3,ExternalLink,FolderOpen,Headphones,Mail,MapPin,MessageCircle,Phone,Search,Shield,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,Building2,Calendar,CheckCircle,Clipboard,Clock,Edit3,ExternalLink,FolderOpen,Headphones,Mail,MapPin,MessageCircle,Phone,Search,Shield,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,Building2,Calendar,CheckCircle,Clipboard,Clock,Edit3,ExternalLink,FolderOpen,Headphones,Mail,MapPin,MessageCircle,Phone,Search,Shield,Star,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction HomePage() {\n    const handleSearch = (query)=>{\n        // Redirect to search results page\n        window.location.href = \"/consulta-tramites?q=\".concat(encodeURIComponent(query));\n    };\n    const handleResultSelect = (result)=>{\n        // Handle result selection\n        if (result.type === \"procedure\") {\n            window.location.href = \"/consulta-tramites?id=\".concat(result.id);\n        } else if (result.type === \"opa\") {\n            window.location.href = \"/consulta-tramites?opa=\".concat(result.id);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-chia-blue-50 via-white to-chia-green-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white/90 backdrop-blur-sm shadow-sm border-b sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-chia-blue-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg\",\n                                            children: \"C\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-lg font-bold text-chia-blue-900\",\n                                                children: \"Municipio de Ch\\xeda\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-chia-blue-600\",\n                                                children: \"Sistema de Atenci\\xf3n Ciudadana\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/dependencias\",\n                                        className: \"text-gray-700 hover:text-chia-blue-600 font-medium\",\n                                        children: \"Dependencias\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/consulta-tramites\",\n                                        className: \"text-gray-700 hover:text-chia-blue-600 font-medium\",\n                                        children: \"Tr\\xe1mites\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"https://pacochia.gov.co/realice-sus-peticiones-quejas-reclamos-sugerencias-y-denuncias/\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"text-gray-700 hover:text-chia-blue-600 font-medium\",\n                                        children: \"Radicar PQRS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/auth/login\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            className: \"border-chia-blue-600 text-chia-blue-600 hover:bg-chia-blue-50\",\n                                            children: \"Iniciar Sesi\\xf3n\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/auth/register\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            className: \"bg-chia-blue-600 hover:bg-chia-blue-700\",\n                                            children: \"Registrarse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"bg-chia-blue-100 text-chia-blue-800 px-4 py-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Portal Ciudadano Digital\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-6xl font-bold text-gray-900 leading-tight\",\n                                    children: [\n                                        \"Servicios municipales\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-chia-blue-600\",\n                                            children: \"al alcance de todos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                                    children: [\n                                        \"Accede a m\\xe1s de \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"800 tr\\xe1mites y servicios\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 31\n                                        }, this),\n                                        \" del Municipio de Ch\\xeda de forma r\\xe1pida, segura y completamente digital. Tu gobierno m\\xe1s cerca que nunca.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto mb-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_search_IntelligentSearchBar__WEBPACK_IMPORTED_MODULE_6__.IntelligentSearchBar, {\n                                onSearch: handleSearch,\n                                onResultSelect: handleResultSelect,\n                                placeholder: \"\\xbfQu\\xe9 tr\\xe1mite necesitas? Ej: licencia construcci\\xf3n, certificado residencia...\",\n                                className: \"w-full\",\n                                showFilters: true,\n                                maxResults: 6\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-chia-blue-600\",\n                                            children: \"108\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Tr\\xe1mites\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-chia-green-600\",\n                                            children: \"721\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"OPAs\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-chia-blue-600\",\n                                            children: \"14\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Dependencias\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-chia-green-600\",\n                                            children: \"24/7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Disponible\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 px-4 sm:px-6 lg:px-8 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Servicios m\\xe1s solicitados\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                                    children: \"Accede r\\xe1pidamente a los tr\\xe1mites y servicios que m\\xe1s utilizan los ciudadanos de Ch\\xeda\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-yellow-50 to-yellow-100 border-0 shadow-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-8 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-yellow-200 rounded-2xl flex items-center justify-center mx-auto mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-8 w-8 text-yellow-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-xl font-bold text-gray-900 mb-3\",\n                                                children: \"Radique aqu\\xed sus peticiones en l\\xednea\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                className: \"text-gray-700 mb-6 leading-relaxed\",\n                                                children: \"Radique aqu\\xed sus peticiones en l\\xednea y dem\\xe1s detalles para acceder a los diferentes tr\\xe1mites de la Alcald\\xeda Municipal de Ch\\xeda.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/peticiones\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                                                    children: [\n                                                        \"Acceder\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-gray-50 to-gray-100 border-0 shadow-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-8 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gray-200 rounded-2xl flex items-center justify-center mx-auto mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-8 w-8 text-gray-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-xl font-bold text-gray-900 mb-3\",\n                                                children: \"Tr\\xe1mites y otros procesos administrativos- OPA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                className: \"text-gray-700 mb-6 leading-relaxed\",\n                                                children: \"Consulte aqu\\xed los requisitos, formularios, costos y dem\\xe1s detalles para acceder a los diferentes tr\\xe1mites de la Alcald\\xeda Municipal de Ch\\xeda.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/consulta-tramites\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                                                    children: [\n                                                        \"Acceder\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-blue-50 to-blue-100 border-0 shadow-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-8 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-blue-200 rounded-2xl flex items-center justify-center mx-auto mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-8 w-8 text-blue-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-xl font-bold text-gray-900 mb-3\",\n                                                children: \"Canales de atenci\\xf3n al ciudadano\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                className: \"text-gray-700 mb-6 leading-relaxed\",\n                                                children: \"Consulta todos los canales de atenci\\xf3n disponibles para tener a la Alcald\\xeda de Ch\\xeda cada vez m\\xe1s cerca.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/atencion-ciudadano\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                                                    children: [\n                                                        \"Acceder\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-green-50 to-green-100 border-0 shadow-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-8 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-green-200 rounded-2xl flex items-center justify-center mx-auto mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-8 w-8 text-green-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-xl font-bold text-gray-900 mb-3\",\n                                                children: \"Documentos relacionados\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                className: \"text-gray-700 mb-6 leading-relaxed\",\n                                                children: \"Consulte aqu\\xed los documentos relacionados con la Atenci\\xf3n al Ciudadano.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/documentos\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                                                    children: [\n                                                        \"Acceder\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-purple-50 to-purple-100 border-0 shadow-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-8 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-purple-200 rounded-2xl flex items-center justify-center mx-auto mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-8 w-8 text-purple-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-xl font-bold text-gray-900 mb-3\",\n                                                children: \"Agendamiento especializado en l\\xednea\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                className: \"text-gray-700 mb-6 leading-relaxed\",\n                                                children: \"Pr\\xf3ximamente podr\\xe1 consultar el listado de servicios disponibles para el agendamiento de una asesor\\xeda especializada por parte de funcionarios de las diferentes dependencias de la administraci\\xf3n central.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/agendamiento\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                                                    children: [\n                                                        \"Acceder\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-indigo-50 to-indigo-100 border-0 shadow-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-8 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-indigo-200 rounded-2xl flex items-center justify-center mx-auto mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-8 w-8 text-indigo-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-xl font-bold text-gray-900 mb-3\",\n                                                children: \"Asistente IA Inteligente\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                className: \"text-gray-700 mb-6 leading-relaxed\",\n                                                children: \"Chatbot con inteligencia artificial que te gu\\xeda paso a paso en tus tr\\xe1mites, responde preguntas frecuentes y te ayuda a encontrar la informaci\\xf3n que necesitas de manera r\\xe1pida y eficiente.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/chat\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                                                    children: [\n                                                        \"Acceder\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-chia-blue-50 to-chia-green-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"\\xbfPor qu\\xe9 elegir nuestro portal digital?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                                    children: \"Modernizamos la gesti\\xf3n municipal para ofrecerte la mejor experiencia ciudadana\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-chia-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-8 w-8 text-chia-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                            children: \"B\\xfasqueda Inteligente\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Encuentra cualquier tr\\xe1mite o servicio con nuestra IA avanzada que entiende tu lenguaje natural\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-chia-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-8 w-8 text-chia-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                            children: \"Disponible 24/7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Accede a todos los servicios las 24 horas del d\\xeda, los 7 d\\xedas de la semana, desde cualquier dispositivo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-8 w-8 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                            children: \"Seguro y Confiable\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Tus datos est\\xe1n protegidos con los m\\xe1s altos est\\xe1ndares de seguridad y privacidad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 px-4 sm:px-6 lg:px-8 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Acceso r\\xe1pido por dependencias\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                                    children: \"Navega directamente a la secretar\\xeda o dependencia que necesitas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/dependencias?dep=alcaldia\",\n                                    className: \"group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"text-center hover:shadow-lg transition-all duration-300 group-hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-chia-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-6 w-6 text-chia-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: \"Alcald\\xeda\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mt-1\",\n                                                    children: \"15 servicios\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/dependencias?dep=planeacion\",\n                                    className: \"group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"text-center hover:shadow-lg transition-all duration-300 group-hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-chia-green-100 rounded-lg flex items-center justify-center mx-auto mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-6 w-6 text-chia-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: \"Planeaci\\xf3n\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mt-1\",\n                                                    children: \"42 servicios\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/dependencias?dep=hacienda\",\n                                    className: \"group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"text-center hover:shadow-lg transition-all duration-300 group-hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-6 w-6 text-yellow-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: \"Hacienda\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mt-1\",\n                                                    children: \"28 servicios\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/dependencias\",\n                                    className: \"group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"text-center hover:shadow-lg transition-all duration-300 group-hover:scale-105 border-2 border-dashed border-chia-blue-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-6 w-6 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: \"Ver todas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mt-1\",\n                                                    children: \"14 dependencias\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                    lineNumber: 340,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 px-4 sm:px-6 lg:px-8 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_faq_FAQSection__WEBPACK_IMPORTED_MODULE_7__.FAQSection, {\n                        title: \"Preguntas Frecuentes\",\n                        description: \"Encuentra respuestas r\\xe1pidas a las consultas m\\xe1s comunes de los ciudadanos\",\n                        showSearch: true,\n                        showStats: true,\n                        className: \"max-w-4xl mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                lineNumber: 403,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 px-4 sm:px-6 lg:px-8 bg-chia-blue-900 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold mb-6\",\n                                        children: \"\\xbfNecesitas ayuda adicional?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-chia-blue-100 text-lg mb-8\",\n                                        children: \"Nuestro equipo est\\xe1 disponible para ayudarte con cualquier consulta o tr\\xe1mite que necesites realizar.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-5 w-5 text-chia-blue-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-chia-blue-100\",\n                                                        children: \"(*************\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-5 w-5 text-chia-blue-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-chia-blue-100\",\n                                                        children: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 text-chia-blue-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-chia-blue-100\",\n                                                        children: \"Carrera 11 # 17-25, Ch\\xeda, Cundinamarca\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 backdrop-blur-sm rounded-2xl p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Horarios de Atenci\\xf3n\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 text-chia-blue-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Lunes - Viernes:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"8:00 AM - 5:00 PM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"S\\xe1bados:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"8:00 AM - 12:00 PM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Portal Digital:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-chia-green-300 font-semibold\",\n                                                        children: \"24/7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 pt-6 border-t border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/chat\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                className: \"w-full bg-chia-green-600 hover:bg-chia-green-700 text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Chatear con Asistente IA\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                lineNumber: 416,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-1 md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-chia-blue-600 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-lg\",\n                                                        children: \"C\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-bold\",\n                                                            children: \"Municipio de Ch\\xeda\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: \"Sistema de Atenci\\xf3n Ciudadana\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 mb-4\",\n                                            children: \"Transformando la gesti\\xf3n municipal a trav\\xe9s de la tecnolog\\xeda para brindar mejores servicios a nuestros ciudadanos.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 text-sm text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_Building2_Calendar_CheckCircle_Clipboard_Clock_Edit3_ExternalLink_FolderOpen_Headphones_Mail_MapPin_MessageCircle_Phone_Search_Shield_Star_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4 text-chia-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Gobierno Digital Certificado\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-4\",\n                                            children: \"Enlaces R\\xe1pidos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/consulta-tramites\",\n                                                        className: \"hover:text-white\",\n                                                        children: \"Consultar Tr\\xe1mites\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/dependencias\",\n                                                        className: \"hover:text-white\",\n                                                        children: \"Dependencias\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/chat\",\n                                                        className: \"hover:text-white\",\n                                                        children: \"Asistente IA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/auth/register\",\n                                                        className: \"hover:text-white\",\n                                                        children: \"Crear Cuenta\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-4\",\n                                            children: \"Soporte\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/ayuda\",\n                                                        className: \"hover:text-white\",\n                                                        children: \"Centro de Ayuda\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/contacto\",\n                                                        className: \"hover:text-white\",\n                                                        children: \"Contacto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/terminos\",\n                                                        className: \"hover:text-white\",\n                                                        children: \"T\\xe9rminos de Uso\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/privacidad\",\n                                                        className: \"hover:text-white\",\n                                                        children: \"Pol\\xedtica de Privacidad\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 476,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2024 Municipio de Ch\\xeda. Todos los derechos reservados.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n                lineNumber: 474,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\app\\\\page.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});