{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef734fa321b7-BOG", "connection": "close", "content-encoding": "br", "content-location": "/procedures?dependency_id=eq.6c51c8c2-5543-4200-a0f7-be231dee9553&select=%2A", "content-profile": "public", "content-range": "0-18/19", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:52 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=1K59g0pWV6aN14IpMubV80VxdnmoQAC1VBdzXtPJlJM-1751425672-*******-KeuyBs2K0ZaxVX3SsEsdt4u6xDcfovyHmfvaB.3gvB.ZL3rJKDZtv2zw.xP0J620opPfqSPC.FK69P6DGwaVWIWIgNr_NTXubTTD9G2M8Kc; path=/; expires=Wed, 02-Jul-25 03:37:52 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/procedures?select=*&dependency_id=eq.6c51c8c2-5543-4200-a0f7-be231dee9553"}, "revalidate": 31536000, "tags": []}