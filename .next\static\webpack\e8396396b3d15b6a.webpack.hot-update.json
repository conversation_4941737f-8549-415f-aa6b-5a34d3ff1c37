{"c": ["app/consulta-tramites/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./components/auth/PermissionButton.tsx", "(app-pages-browser)/./components/auth/ProtectedRoute.tsx", "(app-pages-browser)/./components/auth/RoleGuard.tsx", "(app-pages-browser)/./components/auth/UserRoleDisplay.tsx", "(app-pages-browser)/./components/auth/index.ts", "(app-pages-browser)/./components/layout/ProtectedLayout.tsx", "(app-pages-browser)/./components/navigation/ProtectedNavigation.tsx", "(app-pages-browser)/./components/navigation/RouteGuard.tsx", "(app-pages-browser)/./components/procedures/ProcedureSearchInterface.tsx", "(app-pages-browser)/./components/ui/dropdown-menu.tsx", "(app-pages-browser)/./components/ui/tooltip.tsx", "(app-pages-browser)/./hooks/index.ts", "(app-pages-browser)/./hooks/useAuth.ts", "(app-pages-browser)/./hooks/usePermissions.ts", "(app-pages-browser)/./hooks/useRole.ts", "(app-pages-browser)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-menu/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-tooltip/dist/index.mjs", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-wide-narrow.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Ccomponents%5C%5Cfaq%5C%5CContextualFAQSection.tsx%22%2C%22ids%22%3A%5B%22ContextualFAQSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Ccomponents%5C%5Clayout%5C%5CProtectedLayout.tsx%22%2C%22ids%22%3A%5B%22ProtectedLayout%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Ccomponents%5C%5Cnavigation%5C%5CProtectedNavigation.tsx%22%2C%22ids%22%3A%5B%22ProtectedNavigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Ccomponents%5C%5Cnavigation%5C%5CRouteGuard.tsx%22%2C%22ids%22%3A%5B%22RouteGuard%22%2C%22withRouteGuard%22%2C%22AdminRouteGuard%22%2C%22SuperAdminRouteGuard%22%2C%22CitizenRouteGuard%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-tramites%5C%5Ccomponents%5C%5Cprocedures%5C%5CProcedureSearchInterface.tsx%22%2C%22ids%22%3A%5B%22ProcedureSearchInterface%22%5D%7D&server=false!"]}