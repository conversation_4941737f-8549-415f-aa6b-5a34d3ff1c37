{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef629a027b05-BOG", "connection": "close", "content-encoding": "br", "content-location": "/opas?dependency_id=eq.69ce96bb-faee-403f-92a6-e489e1536d02&select=%2A", "content-profile": "public", "content-range": "*/0", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:49 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=19iCySt.OTdImm.I5mdF3SmOl8O9VZ91ewVFUlHJs5w-1751425669-1.0.1.1-hpV0uvudvLPkrzSNjvvYB7LgIyFDUEYxlO8Lf6HIHye.ck_KxeFPfJoKqHBVXaILrBBVcc9kJGUbk6eEW8Ng7435LvWGXZAdJ7TRjC1ujuo; path=/; expires=Wed, 02-Jul-25 03:37:49 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "1"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/opas?select=*&dependency_id=eq.69ce96bb-faee-403f-92a6-e489e1536d02"}, "revalidate": 31536000, "tags": []}