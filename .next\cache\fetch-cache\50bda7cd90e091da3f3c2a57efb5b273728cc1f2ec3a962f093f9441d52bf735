{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef8dfe60498e-MIA", "connection": "close", "content-encoding": "br", "content-location": "/procedures?dependency_id=eq.8525824e-448e-470d-9911-69c19ad65bc8&select=%2A", "content-profile": "public", "content-range": "0-7/8", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:56 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=ArzLF8N44vWWoxaYTzKnYR62UdDIcq0nSseNHkBwt2c-1751425676-1.0.1.1-7.SIU7Z9YJrHj5RiTaWxWrhpVQgMWu6bVM8nkv4p4oTUgSdE45qxixpHs1TLusJCObkFnLyImlN0_ePwK4vraEhxUrFnb0Icuf3XIWonANw; path=/; expires=Wed, 02-Jul-25 03:37:56 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/procedures?select=*&dependency_id=eq.8525824e-448e-470d-9911-69c19ad65bc8"}, "revalidate": 31536000, "tags": []}