{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958b10cdfd91fa30-BOG", "connection": "close", "content-encoding": "br", "content-location": "/subdependencies?dependency_id=eq.9dbf151a-0111-4880-beb3-0c4b0723ffc7&is_active=eq.true&select=%2A", "content-profile": "public", "content-range": "0-6/7", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:30:38 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=juXocENXfbI4PK.3i1pKwxiKjluhvGIy_zqeuAOL064-1751427038-1.0.1.1-QF6EFpkl3BbGeBMGUuOO.dKZsq6uhdsaDT65B5gIDXoG_LAz8HzPjAAbxioEJLXuQFSYCpVrxugl2zTC6CJI.uRUpNQ51ffCBTbEriA1PXI; path=/; expires=Wed, 02-Jul-25 04:00:38 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "17"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/subdependencies?select=*&dependency_id=eq.9dbf151a-0111-4880-beb3-0c4b0723ffc7&is_active=eq.true"}, "revalidate": 31536000, "tags": []}