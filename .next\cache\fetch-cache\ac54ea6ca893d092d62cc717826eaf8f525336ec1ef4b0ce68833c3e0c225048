{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958b10e43c86fa30-BOG", "connection": "close", "content-encoding": "br", "content-location": "/subdependencies?dependency_id=eq.a716252f-8089-4381-9787-d3f05a85bb76&is_active=eq.true&select=%2A", "content-profile": "public", "content-range": "0-0/1", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:30:42 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=bwqRWWHeuGM0_yt2CrvFfDejSqhKHzgSGdZncCneeag-1751427042-1.0.1.1-Wt8s2qSuU1vBJVbModiQas6W0L_3sgGiMp7EwxBOhOzwSuqOV9cH.1xKh2q.z0DadHeoaEm7vlhTmiVyesH.arlW2zaqoPLMMwpDN8fEHvo; path=/; expires=Wed, 02-Jul-25 04:00:42 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/subdependencies?select=*&dependency_id=eq.a716252f-8089-4381-9787-d3f05a85bb76&is_active=eq.true"}, "revalidate": 31536000, "tags": []}