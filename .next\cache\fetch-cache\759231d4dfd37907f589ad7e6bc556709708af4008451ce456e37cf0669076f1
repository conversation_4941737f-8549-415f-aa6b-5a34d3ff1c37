{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef6ede08da4f-MIA", "connection": "close", "content-encoding": "br", "content-location": "/procedures?dependency_id=eq.b153970f-168b-4f41-8069-1cfbcf9c4929&select=%2A", "content-profile": "public", "content-range": "0-5/6", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:51 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=a49DDF3YzWslmDifNVKw_PU6SAyrYsNDSDRA55oZXbk-1751425671-1.0.1.1-lAkEZTUfbNTdmI_cXQB90SMKC3s60O5jHCSp8oNc5217BZ7rhnY8hk.K9ASs3CQc5bnid4VdMDaTAZcDmLmE6hNoTsNyUi5Nvx2pxWNvDxY; path=/; expires=Wed, 02-Jul-25 03:37:51 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/procedures?select=*&dependency_id=eq.b153970f-168b-4f41-8069-1cfbcf9c4929"}, "revalidate": 31536000, "tags": []}