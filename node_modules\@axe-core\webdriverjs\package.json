{"name": "@axe-core/webdriverjs", "version": "4.10.2", "description": "Provides a method to inject and analyze web pages using axe", "contributors": [{"name": "<PERSON>", "organization": "Deque Systems, Inc.", "url": "http://github.com/dylanb/"}, {"name": "<PERSON><PERSON>", "organization": "Deque Systems, Inc.", "url": "http://github.com/marcysutton/"}, {"name": "<PERSON><PERSON><PERSON>", "organization": "Deque Systems, Inc.", "url": "http://github.com/wilcofiers/"}, {"name": "<PERSON>", "organization": "Deque Systems, Inc.", "url": "http://deque.com/"}, {"name": "<PERSON> (<EMAIL>)"}], "files": ["/dist"], "repository": {"type": "git", "url": "https://github.com/dequelabs/axe-core-npm.git"}, "license": "MPL-2.0", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsup src/index.ts --dts --format esm,cjs", "test": "mocha --timeout 60000 -r ts-node/register 'test/**.spec.ts'", "test:export": "npm run test:esm && npm run test:commonjs && npm run test:ts", "test:esm": "node test/esmTest.mjs", "test:commonjs": "node test/commonjsTest.js", "test:ts": "tsc test/tsTest.ts --noEmit --skipLib<PERSON><PERSON>ck --esModuleInterop", "coverage": "nyc npm run test", "prepare": "npm run build"}, "keywords": ["a11y", "unit", "testing", "tdd", "bdd", "accessibility", "axe", "selenium", "webdriver", "webdriverjs"], "devDependencies": {"@types/chai": "^4.3.3", "@types/express": "^4.17.14", "@types/mocha": "^10.0.0", "@types/node": "^22.0.2", "@types/selenium-webdriver": "^4.1.5", "async-listen": "^3.0.1", "axe-test-fixtures": "github:dequelabs/axe-test-fixtures#v1", "chai": "^4.3.6", "express": "^4.18.2", "mocha": "^10.0.0", "nyc": "^17.1.0", "rimraf": "^6.0.1", "selenium-webdriver": "^4.8.1", "ts-node": "^10.9.1", "tsup": "^8.0.1", "typescript": "^5.2.2"}, "dependencies": {"axe-core": "~4.10.3"}, "peerDependencies": {"selenium-webdriver": ">3.0.0-beta  || >=2.53.1 || >4.0.0-alpha"}, "nyc": {"include": ["src/index.ts"], "extension": [".ts"], "reporter": ["text-summary", "html"], "sourceMap": true, "instrument": true, "checkCoverage": true, "statements": 85, "branches": 85, "functions": 85, "lines": 85}, "gitHead": "1055c5d005e72ad190987c358ebb361631909a85"}