{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958b10de8ac14978-MIA", "connection": "close", "content-encoding": "br", "content-location": "/subdependencies?dependency_id=eq.7d2dc1c1-66c2-4da9-a0f9-5ffc34550bbf&is_active=eq.true&select=%2A", "content-profile": "public", "content-range": "0-8/9", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:30:41 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=QlbiD0zmQAKto7dxuaYNqjvUQeZ3PAp0N3SuWL3fbWI-1751427041-1.0.1.1-AYwXDqDLFdcdnvKTt0ya6fjrfTrBsU.V7F36eMgsdTPwX3JZITaTZI6aQcmFAZePJHevYk5Hd5s2q52zOJMyVa9J61z95fnZodQmEzXG6tI; path=/; expires=Wed, 02-Jul-25 04:00:41 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "1"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/subdependencies?select=*&dependency_id=eq.7d2dc1c1-66c2-4da9-a0f9-5ffc34550bbf&is_active=eq.true"}, "revalidate": 31536000, "tags": []}