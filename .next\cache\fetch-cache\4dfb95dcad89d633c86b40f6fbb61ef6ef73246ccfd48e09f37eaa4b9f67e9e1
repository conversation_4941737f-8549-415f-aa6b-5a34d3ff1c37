{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef977cd43dcc-MIA", "connection": "close", "content-encoding": "br", "content-location": "/opas?dependency_id=eq.2cbdf943-b9df-496c-b18f-9bb5129c506a&select=%2A", "content-profile": "public", "content-range": "0-146/147", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:58 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=ZI8.2zH0hanjflY4NB8wqEBQ.TVHqxY1Skw1RirdxnI-1751425678-1.0.1.1-uaTHU2bGIYodKhDTva97hh0fK5G8UWAbMxPN7lYCkkT4_BoYZT8VyLrxCg5OLgLdNr7YjfJGQQ1MR7p7Mgv.Yft9SQylxRCld7V.SWbxgWA; path=/; expires=Wed, 02-Jul-25 03:37:58 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "8"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/opas?select=*&dependency_id=eq.2cbdf943-b9df-496c-b18f-9bb5129c506a"}, "revalidate": 31536000, "tags": []}