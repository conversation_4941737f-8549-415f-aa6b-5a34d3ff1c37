{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef5a6f86d9a1-MIA", "connection": "close", "content-encoding": "br", "content-location": "/procedures?dependency_id=eq.9dbf151a-0111-4880-beb3-0c4b0723ffc7&select=%2A", "content-profile": "public", "content-range": "*/0", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:48 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=iOedrQkZuTM0pqHYt0mDyDBKiDnKxcfqpxkTOEF.AkM-1751425668-1.0.1.1-qHNGD9Rte7z1wwjJb.0gbSibT0vAcSxoYFmVmFSaBn8rljayPEZeEEDxfQX9_XuU0UldW5xBcwM6jAO1jqh2wBHbsFLyQX5qJX5u6b3LF3U; path=/; expires=Wed, 02-Jul-25 03:37:48 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "1"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/procedures?select=*&dependency_id=eq.9dbf151a-0111-4880-beb3-0c4b0723ffc7"}, "revalidate": 31536000, "tags": []}