{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958b10eb2bcad9a5-MIA", "connection": "close", "content-encoding": "br", "content-location": "/subdependencies?dependency_id=eq.2cbdf943-b9df-496c-b18f-9bb5129c506a&is_active=eq.true&select=%2A", "content-profile": "public", "content-range": "0-4/5", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:30:43 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=LLND0S068YrmApoQBwZYDvCYeoNO4jfrobmMXdCeZNk-1751427043-1.0.1.1-rg3FP3yAaV2nv0gq64Xrxp1ZcNpnzX0NRhBaFwInKUItZL6ro4bXEBth.cyO9IK7nc_sDsiDvz7lCKgQu2zVTNroe7Ar66BcEVNxQ6W.BOY; path=/; expires=Wed, 02-Jul-25 04:00:43 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/subdependencies?select=*&dependency_id=eq.2cbdf943-b9df-496c-b18f-9bb5129c506a&is_active=eq.true"}, "revalidate": 31536000, "tags": []}