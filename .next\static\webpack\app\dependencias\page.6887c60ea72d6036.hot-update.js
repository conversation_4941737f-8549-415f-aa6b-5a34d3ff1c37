"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dependencias/page",{

/***/ "(app-pages-browser)/./components/dependencies/DependencyDetailModal.tsx":
/*!***********************************************************!*\
  !*** ./components/dependencies/DependencyDetailModal.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DependencyDetailModal: function() { return /* binding */ DependencyDetailModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,ChevronRight,Clock,DollarSign,ExternalLink,FileText,Mail,MapPin,Network,Phone,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_loading_states__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/loading-states */ \"(app-pages-browser)/./components/ui/loading-states.tsx\");\n/* harmony import */ var _components_ui_error_states__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/error-states */ \"(app-pages-browser)/./components/ui/error-states.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./components/ui/breadcrumb.tsx\");\n/* harmony import */ var _lib_services_dependencyService__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/services/dependencyService */ \"(app-pages-browser)/./lib/services/dependencyService.ts\");\n/* __next_internal_client_entry_do_not_use__ DependencyDetailModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DependencyDetailModal(param) {\n    let { dependency, isOpen, onClose, onProcedureSelect, showBreadcrumbs = true, onNavigateBack } = param;\n    _s();\n    const [proceduresData, setProceduresData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    // Cargar datos detallados cuando se abre el modal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen && dependency) {\n            loadProceduresData();\n        }\n    }, [\n        isOpen,\n        dependency\n    ]);\n    const loadProceduresData = async ()=>{\n        if (!dependency) return;\n        setIsLoading(true);\n        setError(null);\n        try {\n            await _lib_services_dependencyService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].initialize();\n            const data = await _lib_services_dependencyService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].getProceduresByDependency(dependency.id);\n            setProceduresData(data);\n        } catch (err) {\n            console.error(\"Error cargando datos de procedimientos:\", err);\n            setError(err instanceof Error ? err.message : \"Error al cargar los datos\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleRetry = ()=>{\n        loadProceduresData();\n    };\n    const handleProcedureClick = (procedure)=>{\n        if (onProcedureSelect) {\n            onProcedureSelect(procedure);\n        }\n    };\n    if (!dependency) return null;\n    // Obtener icono de la dependencia\n    const getIcon = ()=>{\n        const iconMap = {\n            crown: _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            shield: _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            banknote: _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            map: _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            users: _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            \"graduation-cap\": _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            \"heart-pulse\": _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            car: _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            building: _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            folder: _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        };\n        const IconComponent = iconMap[dependency.icon || \"building\"] || _barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n            lineNumber: 112,\n            columnNumber: 12\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"max-w-5xl max-h-[90vh] overflow-hidden bg-white/95 backdrop-blur-md border-0 shadow-2xl rounded-3xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    className: \"pb-6\",\n                    children: [\n                        showBreadcrumbs && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_11__.DependencyBreadcrumb, {\n                                dependency: {\n                                    name: dependency.name,\n                                    id: dependency.id\n                                },\n                                onNavigate: onNavigateBack\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg\", dependency.color === \"blue\" ? \"bg-gradient-to-br from-chia-blue-100 to-chia-blue-200 text-chia-blue-600\" : dependency.color === \"green\" ? \"bg-gradient-to-br from-chia-green-100 to-chia-green-200 text-chia-green-600\" : dependency.color === \"purple\" ? \"bg-gradient-to-br from-purple-100 to-purple-200 text-purple-600\" : dependency.color === \"red\" ? \"bg-gradient-to-br from-red-100 to-red-200 text-red-600\" : dependency.color === \"yellow\" ? \"bg-gradient-to-br from-yellow-100 to-yellow-200 text-yellow-600\" : \"bg-gradient-to-br from-gray-100 to-gray-200 text-gray-600\"),\n                                    children: getIcon()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                                            className: \"text-3xl font-bold text-gradient-blue mb-2\",\n                                            children: dependency.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                                            className: \"text-lg\",\n                                            children: [\n                                                dependency.sigla && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"mr-3 text-sm font-semibold\",\n                                                    children: dependency.sigla\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 19\n                                                }, this),\n                                                dependency.description\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                            className: \"grid w-full grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"overview\",\n                                    children: \"Resumen\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"procedures\",\n                                    children: \"Procedimientos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"contact\",\n                                    children: \"Contacto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__.ScrollArea, {\n                            className: \"h-[60vh] mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"overview\",\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                        className: \"p-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-8 w-8 text-chia-green-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-chia-green-700\",\n                                                                children: dependency.tramitesCount\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Tr\\xe1mites\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                        className: \"p-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-8 w-8 text-blue-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-blue-700\",\n                                                                children: dependency.opasCount\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"OPAs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                        className: \"p-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-8 w-8 text-purple-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-purple-700\",\n                                                                children: dependency.subdependenciasCount\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Subdependencias\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                        className: \"p-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-8 w-8 text-chia-blue-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-chia-blue-700\",\n                                                                children: dependency.totalProcedures\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Total\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Acerca de esta dependencia\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-700 leading-relaxed\",\n                                                        children: dependency.description || \"La \".concat(dependency.name, \" es una dependencia municipal encargada de brindar servicios y tr\\xe1mites especializados a los ciudadanos de Ch\\xeda. Cuenta con \").concat(dependency.totalProcedures, \" procedimientos disponibles distribuidos en \").concat(dependency.tramitesCount, \" tr\\xe1mites y \").concat(dependency.opasCount, \" otras prestaciones administrativas.\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"procedures\",\n                                    className: \"space-y-4\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loading_states__WEBPACK_IMPORTED_MODULE_9__.TabContentLoading, {\n                                        message: \"Cargando procedimientos...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_error_states__WEBPACK_IMPORTED_MODULE_10__.ErrorState, {\n                                        type: \"server\",\n                                        title: \"Error al cargar procedimientos\",\n                                        description: error,\n                                        onRetry: handleRetry,\n                                        size: \"md\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, this) : proceduresData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            proceduresData.tramites.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-3 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-chia-green-600 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Tr\\xe1mites (\",\n                                                            proceduresData.tramites.length,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: proceduresData.tramites.map((tramite, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                                className: \"cursor-pointer hover:shadow-md transition-shadow\",\n                                                                onClick: ()=>handleProcedureClick(tramite),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                                    className: \"p-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"font-medium text-gray-900 mb-1\",\n                                                                                        children: tramite.Nombre\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                        lineNumber: 256,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                                                                                        children: [\n                                                                                            tramite[\"\\xbfTiene pago?\"] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center space-x-1\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                        className: \"h-4 w-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                        lineNumber: 262,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: tramite[\"\\xbfTiene pago?\"] === \"No\" ? \"Gratuito\" : tramite[\"\\xbfTiene pago?\"]\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                        lineNumber: 263,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                lineNumber: 261,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            tramite[\"Tiempo de respuesta\"] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center space-x-1\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                                        className: \"h-4 w-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                        lineNumber: 268,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: tramite[\"Tiempo de respuesta\"]\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                        lineNumber: 269,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                lineNumber: 267,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                        lineNumber: 259,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 255,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-gray-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 274,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 21\n                                            }, this),\n                                            proceduresData.opas.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-3 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-5 w-5 text-blue-600 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Otras Prestaciones Administrativas (\",\n                                                            proceduresData.opas.length,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: proceduresData.opas.map((opa, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                                className: \"cursor-pointer hover:shadow-md transition-shadow\",\n                                                                onClick: ()=>handleProcedureClick(opa),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                                    className: \"p-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"font-medium text-gray-900 mb-1\",\n                                                                                        children: opa.OPA\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                        lineNumber: 300,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: opa.subdependencia\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                lineNumber: 304,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center space-x-1\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                        className: \"h-4 w-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                        lineNumber: 306,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Gratuito\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                        lineNumber: 307,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                                lineNumber: 305,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                        lineNumber: 303,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 299,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-gray-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 311,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-12 w-12 text-gray-300 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"No hay procedimientos disponibles\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Esta dependencia no tiene procedimientos registrados actualmente.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"contact\",\n                                    className: \"space-y-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Informaci\\xf3n de Contacto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-chia-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Tel\\xe9fono\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"(601) 884-5500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 347,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-chia-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Email\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 354,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"<EMAIL>\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 355,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 353,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-chia-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 360,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Direcci\\xf3n\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 362,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Carrera 11 No. 17-25, Ch\\xeda, Cundinamarca\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 363,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-chia-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 368,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Horario de Atenci\\xf3n\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 370,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-gray-600\",\n                                                                                children: \"Lunes a Viernes: 8:00 AM - 5:00 PM\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                                lineNumber: 371,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                        lineNumber: 369,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-4 border-t\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            className: \"w-full bg-chia-blue-600 hover:bg-chia-blue-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_ChevronRight_Clock_DollarSign_ExternalLink_FileText_Mail_MapPin_Network_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Visitar P\\xe1gina Web Oficial\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-tramites\\\\components\\\\dependencies\\\\DependencyDetailModal.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n_s(DependencyDetailModal, \"YbWk/uVEdXHGT1WYN1aPTnYG1ks=\");\n_c = DependencyDetailModal;\nvar _c;\n$RefreshReg$(_c, \"DependencyDetailModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dependencies/DependencyDetailModal.tsx\n"));

/***/ })

});