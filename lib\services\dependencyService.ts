import { supabase } from '@/lib/supabase/client'

// Interfaces para tipos de datos
export interface Dependency {
  id: string
  name: string
  sigla: string
  tramitesCount: number
  opasCount: number
  totalProcedures: number
  subdependenciasCount: number
  description?: string
  icon?: string
  color?: string
}

export interface DependencyStats {
  totalDependencies: number
  totalTramites: number
  totalOPAs: number
  totalProcedures: number
  averageProceduresPerDependency: number
}

export interface ProceduresByDependency {
  dependency: Dependency
  tramites: any[]
  opas: any[]
}

/**
 * Servicio para gestionar datos de dependencias municipales
 * Procesa información de trámites y OPAs agrupados por dependencia
 */
class DependencyService {
  private static instance: DependencyService
  private dependencies: Map<string, Dependency> = new Map()
  private initialized = false

  private constructor() {}

  static getInstance(): DependencyService {
    if (!DependencyService.instance) {
      DependencyService.instance = new DependencyService()
    }
    return DependencyService.instance
  }

  /**
   * Inicializa el servicio con las 14 dependencias oficiales del municipio
   */
  async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      // Usar las dependencias oficiales en lugar de procesar datos dinámicamente
      await this.createOfficialDependencies()
      this.initialized = true
    } catch (error) {
      console.error('Error inicializando DependencyService:', error)
      // Fallback: usar el método anterior si falla
      try {
        await this.processDependencies()
        this.initialized = true
      } catch (fallbackError) {
        console.error('Error en fallback:', fallbackError)
        throw error
      }
    }
  }

  /**
   * Procesa los datos de trámites y OPAs para extraer información de dependencias
   */
  private async processDependencies(): Promise<void> {
    const dependencyMap = new Map<string, {
      name: string
      sigla: string
      tramites: any[]
      opas: any[]
    }>()

    try {
      // Obtener trámites desde Supabase
      const { data: tramitesData, error: tramitesError } = await supabase
        .from('procedures')
        .select('*')

      if (tramitesError) {
        console.error('Error obteniendo trámites:', tramitesError)
        throw tramitesError
      }

      // Procesar trámites
      tramitesData?.forEach((tramite: any) => {
        const depCode = tramite.dependency_code || tramite.codigo_dependencia || 'unknown'
        const depName = tramite.dependency_name || tramite.dependencia || 'Dependencia Desconocida'

        if (!dependencyMap.has(depCode)) {
          dependencyMap.set(depCode, {
            name: depName,
            sigla: this.extractSigla(depName),
            tramites: [],
            opas: [],
            subdependenciasCount: 0
          })
        }

        dependencyMap.get(depCode)!.tramites.push(tramite)
      })

      // Obtener OPAs desde Supabase
      const { data: opasData, error: opasError } = await supabase
        .from('opas')
        .select('*')

      if (opasError) {
        console.error('Error obteniendo OPAs:', opasError)
        throw opasError
      }

      // Procesar OPAs
      opasData?.forEach((opa: any) => {
        const depCode = opa.dependency_code || opa.codigo_dependencia || 'unknown'
        const depName = opa.dependency_name || opa.dependencia || 'Dependencia Desconocida'

        if (!dependencyMap.has(depCode)) {
          dependencyMap.set(depCode, {
            name: depName,
            sigla: this.extractSigla(depName),
            tramites: [],
            opas: [],
            subdependenciasCount: 0
          })
        }

        dependencyMap.get(depCode)!.opas.push(opa)
      })

    } catch (error) {
      console.error('Error procesando dependencias:', error)
      throw error
    }

    // Convertir a formato Dependency
    dependencyMap.forEach((depData, depCode) => {
      const dependency: Dependency = {
        id: depCode,
        name: depData.name,
        sigla: depData.sigla,
        tramitesCount: depData.tramites.length,
        opasCount: depData.opas.length,
        totalProcedures: depData.tramites.length + depData.opas.length,
        subdependenciasCount: depData.subdependenciasCount || 0,
        description: this.generateDescription(depData.name),
        icon: this.assignIcon(depData.name),
        color: this.assignColor(depCode)
      }

      this.dependencies.set(depCode, dependency)
    })
  }

  /**
   * Crear las 14 dependencias oficiales del municipio de Chía
   */
  private async createOfficialDependencies(): Promise<void> {
    // Definir las 14 dependencias oficiales del municipio de Chía
    const officialDependencies = [
      {
        id: 'despacho-alcalde',
        name: 'Despacho del Alcalde',
        sigla: 'DA'
      },
      {
        id: 'secretaria-general',
        name: 'Secretaría General',
        sigla: 'SG'
      },
      {
        id: 'secretaria-gobierno',
        name: 'Secretaría de Gobierno y Convivencia',
        sigla: 'SGC'
      },
      {
        id: 'secretaria-hacienda',
        name: 'Secretaría de Hacienda',
        sigla: 'SH'
      },
      {
        id: 'secretaria-planeacion',
        name: 'Secretaría de Planeación',
        sigla: 'SP'
      },
      {
        id: 'secretaria-desarrollo-social',
        name: 'Secretaría de Desarrollo Social',
        sigla: 'SDS'
      },
      {
        id: 'secretaria-educacion',
        name: 'Secretaría de Educación',
        sigla: 'SE'
      },
      {
        id: 'secretaria-salud',
        name: 'Secretaría de Salud',
        sigla: 'SS'
      },
      {
        id: 'secretaria-movilidad',
        name: 'Secretaría de Movilidad',
        sigla: 'SM'
      },
      {
        id: 'secretaria-desarrollo-economico',
        name: 'Secretaría de Desarrollo Económico',
        sigla: 'SDE'
      },
      {
        id: 'secretaria-infraestructura',
        name: 'Secretaría de Infraestructura',
        sigla: 'SI'
      },
      {
        id: 'secretaria-ambiente',
        name: 'Secretaría de Ambiente',
        sigla: 'SA'
      },
      {
        id: 'secretaria-tecnologias',
        name: 'Secretaría de Tecnologías de la Información',
        sigla: 'STI'
      },
      {
        id: 'entidades-descentralizadas',
        name: 'Entidades Descentralizadas',
        sigla: 'ED'
      }
    ]

    // Obtener contadores reales de trámites y OPAs
    const counters = await this.getDependencyCounters()

    // Crear cada dependencia oficial
    officialDependencies.forEach((depInfo, index) => {
      const counter = counters.get(depInfo.name) || { tramites: 0, opas: 0, subdependencies: 0 }

      const dependency: Dependency = {
        id: depInfo.id,
        name: depInfo.name,
        sigla: depInfo.sigla,
        tramitesCount: counter.tramites,
        opasCount: counter.opas,
        totalProcedures: counter.tramites + counter.opas,
        subdependenciasCount: counter.subdependencies,
        description: this.generateDescription(depInfo.name),
        icon: this.assignIcon(depInfo.name),
        color: this.assignColor(depInfo.id)
      }

      this.dependencies.set(depInfo.id, dependency)
    })
  }

  /**
   * Obtener contadores de trámites y OPAs por dependencia
   */
  private async getDependencyCounters(): Promise<Map<string, { tramites: number, opas: number, subdependencies: number }>> {
    const counters = new Map<string, { tramites: number, opas: number, subdependencies: number }>()

    try {
      // Obtener datos de Supabase con JOIN para obtener nombres de dependencias
      const [tramitesResult, opasResult] = await Promise.all([
        supabase
          .from('procedures')
          .select(`
            id,
            dependencies!inner(name, code)
          `),
        supabase
          .from('opas')
          .select(`
            id,
            dependencies!inner(name, code)
          `)
      ])

      // Procesar trámites
      tramitesResult.data?.forEach((item: any) => {
        const depName = this.mapToOfficialDependency(item.dependencies?.name)
        if (!counters.has(depName)) {
          counters.set(depName, { tramites: 0, opas: 0, subdependencies: 0 })
        }
        counters.get(depName)!.tramites++
      })

      // Procesar OPAs
      opasResult.data?.forEach((item: any) => {
        const depName = this.mapToOfficialDependency(item.dependencies?.name)
        if (!counters.has(depName)) {
          counters.set(depName, { tramites: 0, opas: 0, subdependencies: 0 })
        }
        counters.get(depName)!.opas++
      })

    } catch (error) {
      console.error('Error obteniendo contadores:', error)
    }

    return counters
  }

  /**
   * Mapear nombres de dependencias de los datos a las dependencias oficiales
   */
  private mapToOfficialDependency(originalName: string): string {
    if (!originalName) return 'Entidades Descentralizadas'

    const mappings: { [key: string]: string } = {
      'Despacho Alcalde': 'Despacho del Alcalde',
      'Secretaria General': 'Secretaría General',
      'Secretaria de Gobierno y Convivencia': 'Secretaría de Gobierno y Convivencia',
      'Secretaria de Hacienda': 'Secretaría de Hacienda',
      'Secretaria de Planeacion': 'Secretaría de Planeación',
      'Secretaria de Desarrollo Social': 'Secretaría de Desarrollo Social',
      'Secretaria de Educacion': 'Secretaría de Educación',
      'Secretaria de Salud': 'Secretaría de Salud',
      'Secretaria de Movilidad': 'Secretaría de Movilidad',
      'Secretaria para el Desarrollo Economico': 'Secretaría de Desarrollo Económico',
      'Secretaria de Infraestructura': 'Secretaría de Infraestructura',
      'Secretaria de Medio Ambiente': 'Secretaría de Ambiente',
      'Secretaria de Participacion Ciudadana y Accion Comunitaria': 'Secretaría de Gobierno y Convivencia',
      'Descentralizados': 'Entidades Descentralizadas'
    }

    return mappings[originalName] || originalName
  }

  /**
   * Extrae sigla de un nombre de dependencia
   */
  private extractSigla(name: string): string {
    if (name.includes('Secretaría')) {
      const words = name.replace('Secretaría de ', '').split(' ')
      return words.map(word => word.charAt(0).toUpperCase()).join('')
    }
    
    const words = name.split(' ')
    if (words.length === 1) return words[0].substring(0, 3).toUpperCase()
    return words.map(word => word.charAt(0).toUpperCase()).join('').substring(0, 4)
  }

  /**
   * Genera descripción para una dependencia
   */
  private generateDescription(name: string): string {
    const descriptions: { [key: string]: string } = {
      'Despacho del Alcalde': 'Oficina principal del Alcalde Municipal y coordinación general',
      'Secretaría General': 'Gestión administrativa, jurídica y de archivo municipal',
      'Secretaría de Gobierno y Convivencia': 'Orden público, convivencia ciudadana y participación comunitaria',
      'Secretaría de Hacienda': 'Administración financiera, tributaria y presupuestal municipal',
      'Secretaría de Planeación': 'Ordenamiento territorial, desarrollo urbano y gestión de proyectos',
      'Secretaría de Desarrollo Social': 'Programas sociales, atención a población vulnerable y desarrollo comunitario',
      'Secretaría de Educación': 'Administración del sistema educativo municipal y programas pedagógicos',
      'Secretaría de Salud': 'Vigilancia epidemiológica, control sanitario y salud pública',
      'Secretaría de Movilidad': 'Gestión del tránsito, transporte público y movilidad sostenible',
      'Secretaría de Desarrollo Económico': 'Promoción empresarial, turismo y desarrollo económico local',
      'Secretaría de Infraestructura': 'Obras públicas, mantenimiento vial y proyectos de infraestructura',
      'Secretaría de Ambiente': 'Gestión ambiental, recursos naturales y sostenibilidad',
      'Secretaría de Tecnologías de la Información': 'Sistemas de información, gobierno digital y tecnología',
      'Entidades Descentralizadas': 'Empresas de servicios públicos y entidades adscritas al municipio'
    }

    return descriptions[name] || `Gestión de procedimientos administrativos de ${name}`
  }

  /**
   * Asigna icono representativo para una dependencia
   */
  private assignIcon(name: string): string {
    const icons: { [key: string]: string } = {
      'Despacho del Alcalde': 'crown',
      'Secretaría General': 'folder',
      'Secretaría de Gobierno y Convivencia': 'shield',
      'Secretaría de Hacienda': 'banknote',
      'Secretaría de Planeación': 'map',
      'Secretaría de Desarrollo Social': 'users',
      'Secretaría de Educación': 'graduation-cap',
      'Secretaría de Salud': 'heart-pulse',
      'Secretaría de Movilidad': 'car',
      'Secretaría de Desarrollo Económico': 'building',
      'Secretaría de Infraestructura': 'building',
      'Secretaría de Ambiente': 'folder',
      'Secretaría de Tecnologías de la Información': 'folder',
      'Entidades Descentralizadas': 'building'
    }

    return icons[name] || 'folder'
  }

  /**
   * Asigna color temático para una dependencia
   */
  private assignColor(depCode: string): string {
    const colors = [
      'bg-chia-blue-100 border-chia-blue-300 hover:bg-chia-blue-200',
      'bg-chia-green-100 border-chia-green-300 hover:bg-chia-green-200',
      'bg-blue-100 border-blue-300 hover:bg-blue-200',
      'bg-green-100 border-green-300 hover:bg-green-200',
      'bg-purple-100 border-purple-300 hover:bg-purple-200',
      'bg-orange-100 border-orange-300 hover:bg-orange-200',
      'bg-teal-100 border-teal-300 hover:bg-teal-200',
      'bg-indigo-100 border-indigo-300 hover:bg-indigo-200',
      'bg-pink-100 border-pink-300 hover:bg-pink-200'
    ]

    const index = parseInt(depCode) % colors.length
    return colors[index]
  }

  /**
   * Obtiene todas las dependencias directamente desde la base de datos
   */
  async getAllDependencies(): Promise<Dependency[]> {
    try {
      // Obtener dependencias con contadores desde la base de datos
      const { data: dependenciesData, error: dependenciesError } = await supabase
        .from('dependencies')
        .select('id, name, code, acronym')
        .order('name')

      if (dependenciesError) {
        console.error('Error obteniendo dependencias:', dependenciesError)
        return []
      }

      // Obtener contadores de trámites y OPAs para cada dependencia
      const dependencies: Dependency[] = []

      for (const dep of dependenciesData || []) {
        // Contar trámites
        const { count: tramitesCount } = await supabase
          .from('procedures')
          .select('*', { count: 'exact', head: true })
          .eq('dependency_id', dep.id)

        // Contar OPAs
        const { count: opasCount } = await supabase
          .from('opas')
          .select('*', { count: 'exact', head: true })
          .eq('dependency_id', dep.id)

        const dependency: Dependency = {
          id: dep.id,
          name: dep.name,
          sigla: dep.acronym || dep.code || '',
          tramitesCount: tramitesCount || 0,
          opasCount: opasCount || 0,
          totalProcedures: (tramitesCount || 0) + (opasCount || 0),
          subdependenciasCount: 0, // TODO: Implementar si es necesario
          description: this.generateDescription(dep.name),
          icon: this.assignIcon(dep.name),
          color: this.assignColor(dep.id)
        }

        dependencies.push(dependency)
      }

      return dependencies.sort((a, b) => b.totalProcedures - a.totalProcedures)
    } catch (error) {
      console.error('Error en getAllDependencies:', error)
      return []
    }
  }

  /**
   * Obtiene una dependencia por ID
   */
  async getDependencyById(id: string): Promise<Dependency | null> {
    await this.initialize()
    return this.dependencies.get(id) || null
  }

  /**
   * Obtiene estadísticas generales de dependencias directamente desde la base de datos
   */
  async getDependencyStats(): Promise<DependencyStats> {
    try {
      // Contar dependencias
      const { count: totalDependencies } = await supabase
        .from('dependencies')
        .select('*', { count: 'exact', head: true })

      // Contar trámites
      const { count: totalTramites } = await supabase
        .from('procedures')
        .select('*', { count: 'exact', head: true })

      // Contar OPAs
      const { count: totalOPAs } = await supabase
        .from('opas')
        .select('*', { count: 'exact', head: true })

      const totalProcedures = (totalTramites || 0) + (totalOPAs || 0)
      const averageProceduresPerDependency = totalDependencies > 0
        ? Math.round(totalProcedures / totalDependencies)
        : 0

      return {
        totalDependencies: totalDependencies || 0,
        totalTramites: totalTramites || 0,
        totalOPAs: totalOPAs || 0,
        totalProcedures,
        averageProceduresPerDependency
      }
    } catch (error) {
      console.error('Error en getDependencyStats:', error)
      return {
        totalDependencies: 0,
        totalTramites: 0,
        totalOPAs: 0,
        totalProcedures: 0,
        averageProceduresPerDependency: 0
      }
    }
  }

  /**
   * Busca dependencias por nombre
   */
  async searchDependencies(query: string): Promise<Dependency[]> {
    await this.initialize()
    
    const searchTerm = query.toLowerCase().trim()
    if (!searchTerm) return this.getAllDependencies()

    return Array.from(this.dependencies.values())
      .filter(dep => 
        dep.name.toLowerCase().includes(searchTerm) ||
        dep.sigla.toLowerCase().includes(searchTerm) ||
        (dep.description && dep.description.toLowerCase().includes(searchTerm))
      )
      .sort((a, b) => b.totalProcedures - a.totalProcedures)
  }

  /**
   * Obtiene procedimientos de una dependencia específica
   */
  async getProceduresByDependency(dependencyId: string): Promise<ProceduresByDependency | null> {
    await this.initialize()

    const dependency = this.dependencies.get(dependencyId)
    if (!dependency) return null

    try {
      // Primero obtener el UUID de la dependencia desde la base de datos
      const { data: depData, error: depError } = await supabase
        .from('dependencies')
        .select('id, name')
        .ilike('name', `%${dependency.name.replace('Secretaría', 'Secretaria')}%`)
        .single()

      if (depError || !depData) {
        console.error('Error obteniendo dependencia:', depError)
        return {
          dependency,
          tramites: [],
          opas: []
        }
      }

      // Obtener trámites de esta dependencia desde Supabase
      const { data: tramites, error: tramitesError } = await supabase
        .from('procedures')
        .select(`
          *,
          dependencies!inner(name, code)
        `)
        .eq('dependency_id', depData.id)

      if (tramitesError) {
        console.error('Error obteniendo trámites:', tramitesError)
      }

      // Obtener OPAs de esta dependencia desde Supabase
      const { data: opas, error: opasError } = await supabase
        .from('opas')
        .select(`
          *,
          dependencies!inner(name, code)
        `)
        .eq('dependency_id', depData.id)

      if (opasError) {
        console.error('Error obteniendo OPAs:', opasError)
      }

      return {
        dependency,
        tramites: tramites || [],
        opas: opas || []
      }
    } catch (error) {
      console.error('Error obteniendo procedimientos por dependencia:', error)
      return {
        dependency,
        tramites: [],
        opas: []
      }
    }
  }

  /**
   * Obtiene las dependencias más populares (con más procedimientos)
   */
  async getTopDependencies(limit: number = 6): Promise<Dependency[]> {
    await this.initialize()
    
    return Array.from(this.dependencies.values())
      .sort((a, b) => b.totalProcedures - a.totalProcedures)
      .slice(0, limit)
  }
}

export default DependencyService.getInstance()
