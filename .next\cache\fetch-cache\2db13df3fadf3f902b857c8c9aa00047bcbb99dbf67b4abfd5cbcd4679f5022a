{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958b10d198e6ed30-MIA", "connection": "close", "content-encoding": "br", "content-location": "/subdependencies?dependency_id=eq.69ce96bb-faee-403f-92a6-e489e1536d02&is_active=eq.true&select=%2A", "content-profile": "public", "content-range": "0-6/7", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:30:39 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=ioGWpkmok1Ic.tud1HQyun3PKhLhb33iCwigt_B6XdU-1751427039-1.0.1.1-o8AUXfg7eux6tcUn2FiJ0TQqibsCV9EzFIy25DueiSPCQh50r7Wy_zz3XnjTamTEd_xYSzXyhK2jEQYCqtBLiRLQlHg74MxGJ_CVTh2XXNc; path=/; expires=Wed, 02-Jul-25 04:00:39 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "15"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/subdependencies?select=*&dependency_id=eq.69ce96bb-faee-403f-92a6-e489e1536d02&is_active=eq.true"}, "revalidate": 31536000, "tags": []}