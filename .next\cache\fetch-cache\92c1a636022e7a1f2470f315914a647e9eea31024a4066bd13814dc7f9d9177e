{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958b10d75d123711-MIA", "connection": "close", "content-encoding": "br", "content-location": "/subdependencies?dependency_id=eq.c7c5b677-0a1f-4b82-be62-b3feb55285ae&is_active=eq.true&select=%2A", "content-profile": "public", "content-range": "0-3/4", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:30:39 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=.KneoEmn0OHYZXVfzJ1epOAkr6crQ3wNaH_3WexuqSg-1751427039-*******-rIPnM7bEMxXhZISZ9PtH0umSgb0mciA7Yt0FZsMqHvqc3t94zmy2NiVTBaEXLYk.PLMYChD6McfNGLEkN2s_7V4amhjh29InmFHbJn2x9nM; path=/; expires=Wed, 02-Jul-25 04:00:39 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/subdependencies?select=*&dependency_id=eq.c7c5b677-0a1f-4b82-be62-b3feb55285ae&is_active=eq.true"}, "revalidate": 31536000, "tags": []}