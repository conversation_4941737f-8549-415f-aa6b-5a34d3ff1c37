{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef868c67da23-MIA", "connection": "close", "content-encoding": "br", "content-location": "/opas?dependency_id=eq.a716252f-8089-4381-9787-d3f05a85bb76&select=%2A", "content-profile": "public", "content-range": "0-28/29", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:55 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=sSTrThJ1S1wr0K2oYGYmEeXdt8OpB601fqhV4ed2CPk-1751425675-1.0.1.1-qe8rmxKzoWt8VT21ZMeKTbI5W3cNF6NDWlvG.4kwekHhiGkwOY0GfGByxkR75eITHcDPshdQPEp2bvyGY0eVzukUNHLoeMrV13voXLAWBXo; path=/; expires=Wed, 02-Jul-25 03:37:55 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/opas?select=*&dependency_id=eq.a716252f-8089-4381-9787-d3f05a85bb76"}, "revalidate": 31536000, "tags": []}