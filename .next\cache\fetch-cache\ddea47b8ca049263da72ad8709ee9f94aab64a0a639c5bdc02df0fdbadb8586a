{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958b10e2dd804c2d-MIA", "connection": "close", "content-encoding": "br", "content-location": "/subdependencies?dependency_id=eq.12c41975-e14d-49ea-9d1e-4d771dd6936c&is_active=eq.true&select=%2A", "content-profile": "public", "content-range": "0-3/4", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:30:41 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=7d_3dXDyBmXE7yvLBg1iTS_UOBTAO4zSav1pZLUjTMY-1751427041-1.0.1.1-w_ajLyzVjBTGUMRdF4QiewWj4vHV3wWnevH84YVpU0UCR7TjKRDfsAmeP8cLNq7VKVYVi3GNBHzKX18wJ.v8w2Rf0PcPFpB6Skgop7TEpjU; path=/; expires=Wed, 02-Jul-25 04:00:41 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/subdependencies?select=*&dependency_id=eq.12c41975-e14d-49ea-9d1e-4d771dd6936c&is_active=eq.true"}, "revalidate": 31536000, "tags": []}