{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "958aef7f99d0db3f-BOG", "connection": "close", "content-encoding": "br", "content-location": "/procedures?dependency_id=eq.12c41975-e14d-49ea-9d1e-4d771dd6936c&select=%2A", "content-profile": "public", "content-range": "*/0", "content-type": "application/json; charset=utf-8", "date": "Wed, 02 Jul 2025 03:07:54 GMT", "preference-applied": "count=exact", "sb-gateway-version": "1", "sb-project-ref": "zeieudvbhlrlnfkwejoh", "server": "cloudflare", "set-cookie": "__cf_bm=EcMhevgVLHxfZuAxLQKzC0uJN7omqlRN1Ogs4Jr.BLM-1751425674-1.0.1.1-StPiTLmHUjxM2weSDFPGGD7Yml1F.E70vcmORBNfhjUjeY36OWZ29fczwxzzY_Zcm_OoyjMqFAgFBvAKui77f1e.mRXS_jFA_i3W7LypTDk; path=/; expires=Wed, 02-Jul-25 03:37:54 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "1"}, "body": "", "status": 200, "url": "https://zeieudvbhlrlnfkwejoh.supabase.co/rest/v1/procedures?select=*&dependency_id=eq.12c41975-e14d-49ea-9d1e-4d771dd6936c"}, "revalidate": 31536000, "tags": []}