"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(request) {\n    let response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n        request: {\n            headers: request.headers\n        }\n    });\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://zeieudvbhlrlnfkwejoh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InplaWV1ZHZiaGxybG5ma3dlam9oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDk1MDEsImV4cCI6MjA2Njg4NTUwMX0.sOImH-XXxxVjjUZhWwYt6KK6dpfCBK2wvT2rnPmlC50\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n                request.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n                response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: request.headers\n                    }\n                });\n                response.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n            },\n            remove (name, options) {\n                request.cookies.set({\n                    name,\n                    value: \"\",\n                    ...options\n                });\n                response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: request.headers\n                    }\n                });\n                response.cookies.set({\n                    name,\n                    value: \"\",\n                    ...options\n                });\n            }\n        }\n    });\n    // Refresh session if expired - required for Server Components\n    const { data: { user }, error } = await supabase.auth.getUser();\n    const { pathname } = request.nextUrl;\n    // Define protected routes\n    const protectedRoutes = [\n        \"/dashboard\",\n        \"/tramites\",\n        \"/opas\",\n        \"/chat\",\n        \"/perfil\",\n        \"/notificaciones\",\n        \"/admin\"\n    ];\n    // Define admin-only routes\n    const adminRoutes = [\n        \"/admin\"\n    ];\n    // Define public routes that don't require authentication\n    const publicRoutes = [\n        \"/\",\n        \"/auth/login\",\n        \"/auth/register\",\n        \"/auth/forgot-password\",\n        \"/auth/reset-password\",\n        \"/auth/callback\",\n        \"/procedimientos\",\n        \"/informacion\",\n        \"/contacto\",\n        \"/consulta-tramites\",\n        \"/dependencias\"\n    ];\n    // Check if the current path is protected\n    const isProtectedRoute = protectedRoutes.some((route)=>pathname.startsWith(route));\n    // Check if the current path is admin-only\n    const isAdminRoute = adminRoutes.some((route)=>pathname.startsWith(route));\n    // Check if the current path is public\n    const isPublicRoute = publicRoutes.some((route)=>pathname === route || pathname.startsWith(route));\n    // If user is not authenticated and trying to access protected route\n    if (!user && isProtectedRoute) {\n        const redirectUrl = new URL(\"/auth/login\", request.url);\n        redirectUrl.searchParams.set(\"redirectTo\", pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n    }\n    // If user is authenticated and trying to access auth pages, redirect to dashboard\n    if (user && pathname.startsWith(\"/auth/\") && pathname !== \"/auth/callback\") {\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n    }\n    // Check admin permissions for admin routes\n    if (user && isAdminRoute) {\n        try {\n            // Get user profile with role information\n            const { data: profile } = await supabase.from(\"profiles\").select(`\n          *,\n          role:roles(*)\n        `).eq(\"id\", user.id).single();\n            // Check if user has admin or super_admin role\n            const hasAdminAccess = profile?.role?.name === \"admin\" || profile?.role?.name === \"super_admin\";\n            if (!hasAdminAccess) {\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n            }\n        } catch (error) {\n            console.error(\"Error checking admin permissions:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n        }\n    }\n    // Handle root redirect based on authentication status\n    if (pathname === \"/\") {\n        if (user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n        }\n    // For non-authenticated users, show the public landing page\n    }\n    return response;\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */ \"/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});